# 统一响应格式 v2.0 更新日志

## 📋 概述

本次更新将算法平台的所有API响应格式统一为v2.0规范，提供更简洁、灵活和一致的数据结构。

## 🎯 更新目标

1. **统一响应结构** - 所有算法API使用相同的响应格式
2. **简化数据格式** - 边界框使用数组格式，更紧凑高效
3. **统一字段命名** - 使用"label"替代"class_name"
4. **灵活扩展机制** - attributes字段支持算法特有数据
5. **丰富元数据** - 提供处理时间、图像信息、时间戳等

## 🔄 主要变更

### 1. 响应结构调整

**v1.0 格式**:
```json
{
  "success": true,
  "processing_time_ms": 45,
  "results": {...}
}
```

**v2.0 格式**:
```json
{
  "success": true,
  "error": null,
  "data": {...},
  "metadata": {
    "processing_time_ms": 45.2,
    "image_shape": [1080, 1920, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

### 2. 边界框格式变更

**v1.0 格式**:
```json
"bbox": {
  "x_min": 100, "y_min": 150,
  "x_max": 250, "y_max": 300,
  "width": 150, "height": 150
}
```

**v2.0 格式**:
```json
"bbox": [100, 150, 250, 300],
"width": 150,
"height": 150,
"center": [175, 225]
```

### 3. 字段名称统一

- `class_name` → `label`
- `total_detections` → `num_detections`
- `classes_detected` → `class_counts`

### 4. 新增扩展字段

```json
"attributes": {
  "landmarks": [[x1, y1], [x2, y2]],
  "feature_vector": [0.1, 0.2, 0.3],
  "track_id": 15,
  "pose": {"yaw": 10.5, "pitch": -5.2},
  "quality_score": 0.95,
  "custom_field": "custom_value"
}
```

## 📁 更新文件列表

### 1. 规范文档
- ✅ `algorithms/ALGORITHM_API_SPECIFICATION.md` - 添加统一响应格式规范章节
- ✅ 更新所有API示例以使用新格式
- ✅ 添加迁移指南

### 2. 模板算法包
- ✅ `algorithms/template/src/models/detection.py` - 重写数据模型
- ✅ `algorithms/template/src/api/detection.py` - 更新同步检测API
- ✅ `algorithms/template/src/api/async_processing.py` - 更新异步处理API
- ✅ `algorithms/template/README.md` - 更新文档说明
- ✅ `algorithms/template/test_unified_response.py` - 新增测试脚本

## 🧪 测试验证

创建了完整的测试脚本验证新格式：

```bash
cd algorithms/template
uv run python test_unified_response.py
```

测试覆盖：
- ✅ 检测结果模型
- ✅ 成功响应格式
- ✅ 错误响应格式
- ✅ 边界框格式和几何计算
- ✅ 扩展属性字段

## 📊 格式对比示例

### 检测算法响应

**v2.0 统一格式**:
```json
{
  "success": true,
  "error": null,
  "data": {
    "detections": [
      {
        "bbox": [100, 150, 250, 300],
        "confidence": 0.92,
        "label": "person",
        "class_id": 0,
        "center": [175, 225],
        "width": 150,
        "height": 150,
        "attributes": {
          "pose": {"standing": true},
          "track_id": 15
        }
      }
    ],
    "summary": {
      "num_detections": 1,
      "class_counts": {"person": 1},
      "confidence_stats": {
        "min": 0.92, "max": 0.92, "avg": 0.92
      }
    }
  },
  "metadata": {
    "processing_time_ms": 45.2,
    "image_shape": [1080, 1920, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z",
    "model_info": {
      "name": "algorithm_name",
      "version": "1.0.0"
    }
  }
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "INVALID_FILE_FORMAT",
    "message": "不支持的文件格式",
    "details": "仅支持图片和视频文件",
    "timestamp": "2025-07-30T12:55:00.123Z"
  },
  "data": null,
  "metadata": {
    "processing_time_ms": 12.5,
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

## 🔧 开发者指南

### 1. 使用新模板

```bash
# 复制模板
cp -r algorithms/template algorithms/your-algorithm

# 修改算法实现
# 所有响应会自动使用v2.0格式
```

### 2. 创建检测结果

```python
from src.models.detection import Detection, DetectionData, create_success_response

# 创建检测结果
detection = Detection(
    bbox=[100, 150, 250, 300],
    confidence=0.92,
    label="person",
    class_id=0,
    attributes={
        "track_id": 15,
        "pose": {"standing": True}
    }
)

# 创建成功响应
response = create_success_response(
    data=detection_data,
    metadata=metadata
)
```

### 3. 处理错误

```python
from src.models.detection import create_error_response

# 创建错误响应
response = create_error_response(
    error_code="INVALID_FILE_FORMAT",
    error_message="不支持的文件格式",
    error_details="仅支持图片和视频文件"
)
```

## 🚀 后续计划

1. **现有算法迁移** - 逐步将现有算法更新到v2.0格式
2. **前端适配** - 更新前端代码以处理新的响应格式
3. **文档完善** - 补充更多使用示例和最佳实践
4. **性能优化** - 基于新格式优化数据传输和处理效率

## 📞 支持

如有问题或建议，请联系算法平台团队。

---

**版本**: v2.0.0  
**更新时间**: 2025-07-30  
**维护者**: 算法平台团队
