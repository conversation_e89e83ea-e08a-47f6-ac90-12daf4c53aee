# 算法集成指南

本指南详细说明如何将新的AI算法集成到算法管理平台中。

## 🎯 集成流程概览

```mermaid
graph TD
    A[开发算法] --> B[遵循API规范]
    B --> C[使用模板开发]
    C --> D[构建Docker镜像]
    D --> E[添加标签]
    E --> F[测试API接口]
    F --> G[部署到平台]
    G --> H[平台自动发现]
```

## 📋 前置要求

### 开发环境
- Python 3.11+
- uv包管理器 (推荐)
- Docker 20.10+
- 算法模型文件
- 测试数据集

### 技能要求
- Python编程
- FastAPI框架基础
- uv包管理器使用
- Docker容器化
- 基本的机器学习知识

## 🚀 快速集成步骤

### 1. 安装uv包管理器
```bash
# 安装uv（如果未安装）
pip install uv

# 或使用官方安装脚本
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. 复制模板项目
```bash
# 进入算法目录
cd algorithms/

# 复制模板
cp -r template your-algorithm-name
cd your-algorithm-name
```

### 3. 初始化项目环境
```bash
# 安装项目依赖
uv sync

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows
```

### 4. 配置算法信息
编辑 `pyproject.toml`：
```toml
[project]
name = "your-algorithm-name"
version = "1.0.0"
description = "你的算法描述"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    # 添加你的依赖...
]
```

编辑 `Dockerfile` 中的标签：
```dockerfile
LABEL algorithm.platform="true"
LABEL algorithm.name="你的算法名称"
LABEL algorithm.type="目标检测"  # 目标检测/图像分类/人脸识别/语义分割
LABEL algorithm.version="1.0.0"
LABEL algorithm.description="算法功能描述"
```

编辑 `src/config.py`：
```python
class Settings(BaseSettings):
    algorithm_name: str = "你的算法名称"
    algorithm_type: str = "目标检测"
    algorithm_description: str = "算法功能描述"
    model_path: str = "models/your_model.pt"
    # 其他配置...
```

### 5. 实现核心算法
修改 `src/core/algorithm.py`：

```python
async def _load_model(self):
    """加载你的模型"""
    # 示例：PyTorch模型加载
    import torch
    self.model = torch.load(settings.model_path, map_location=self.device)
    self.model.eval()
    
    # 示例：TensorFlow模型加载
    # import tensorflow as tf
    # self.model = tf.keras.models.load_model(settings.model_path)

async def _run_inference(self, image: np.ndarray, parameters: DetectionRequest):
    """实现推理逻辑"""
    # 1. 预处理
    processed_image = self._preprocess(image)
    
    # 2. 推理
    outputs = self._inference(processed_image)
    
    # 3. 后处理
    detections = self._postprocess(outputs, parameters)
    
    return detections

def _preprocess(self, image: np.ndarray):
    """图像预处理"""
    # 调整大小、归一化等
    resized = cv2.resize(image, (640, 640))
    normalized = resized / 255.0
    return normalized

def _inference(self, image):
    """模型推理"""
    # PyTorch示例
    with torch.no_grad():
        tensor = torch.from_numpy(image).float()
        outputs = self.model(tensor)
    return outputs

def _postprocess(self, outputs, parameters):
    """后处理"""
    detections = []
    # 解析模型输出，生成Detection对象
    # ...
    return detections
```

### 6. 添加依赖
编辑 `pyproject.toml`：
```toml
[project]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    # 添加你的特定依赖
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    # 或者
    # "tensorflow>=2.13.0",
    # 其他依赖...
]
```

然后更新锁文件：
```bash
uv lock
```

### 7. 放置模型文件
```bash
# 创建模型目录
mkdir -p models/

# 复制模型文件
cp /path/to/your/model.pt models/
```

### 8. 本地开发和测试
```bash
# 安装新依赖
uv sync

# 启动开发服务器
uv run python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# 运行测试
uv run pytest tests/
```

### 9. 构建和测试Docker镜像
```bash
# 构建镜像
./build.sh build

# 测试镜像
./build.sh test

# 完整测试
./build.sh all
```

## 🔧 高级配置

### 自定义数据模型
如果你的算法输出格式特殊，修改 `src/models/detection.py`：

```python
class CustomDetection(BaseModel):
    """自定义检测结果"""
    class_id: int
    class_name: str
    confidence: float
    bbox: BoundingBox
    
    # 添加你的特定字段
    pose_keypoints: Optional[List[Point]] = None
    segmentation_mask: Optional[str] = None
    custom_attributes: Optional[Dict[str, Any]] = None
```

### 异步处理优化
对于耗时较长的算法，优化异步处理：

```python
async def _run_inference_async(self, image: np.ndarray, parameters: DetectionRequest):
    """异步推理，支持进度回调"""
    # 分阶段处理，更新进度
    await self._update_progress(10, "开始预处理...")
    processed = self._preprocess(image)
    
    await self._update_progress(50, "执行推理...")
    outputs = self._inference(processed)
    
    await self._update_progress(90, "后处理...")
    detections = self._postprocess(outputs, parameters)
    
    await self._update_progress(100, "处理完成")
    return detections
```

### GPU支持
如果使用GPU，修改Dockerfile：

```dockerfile
# 使用CUDA基础镜像
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

# 安装CUDA相关依赖
RUN apt-get update && apt-get install -y \
    cuda-toolkit-11-8 \
    && rm -rf /var/lib/apt/lists/*
```

配置文件：
```python
# 自动检测设备
model_device: str = "cuda" if torch.cuda.is_available() else "cpu"
```

## 🧪 测试和验证

### 1. 单元测试
创建 `tests/test_algorithm.py`：

```python
import pytest
from src.core.algorithm import AlgorithmEngine

@pytest.mark.asyncio
async def test_algorithm_initialization():
    engine = AlgorithmEngine()
    await engine.initialize()
    assert engine.is_initialized

@pytest.mark.asyncio
async def test_detection():
    engine = AlgorithmEngine()
    await engine.initialize()
    
    # 加载测试图像
    with open("test_data/test_image.jpg", "rb") as f:
        image_data = f.read()
    
    # 执行检测
    results = await engine.detect(image_data, "test.jpg", DetectionRequest())
    
    # 验证结果
    assert results.summary.total_detections >= 0
```

### 2. API测试
```bash
# 启动容器
docker run -d -p 8000:8000 --name test-algo your-algorithm:latest

# 测试健康检查
curl http://localhost:8000/api/v1/health

# 测试算法信息
curl http://localhost:8000/api/v1/info

# 测试检测接口
curl -X POST \
  -F "file=@test_image.jpg" \
  -F "parameters={\"confidence_threshold\": 0.5}" \
  http://localhost:8000/api/v1/detect
```

### 3. 性能测试
```bash
# 使用ab进行压力测试
ab -n 100 -c 5 -p test_data.json -T application/json \
   http://localhost:8000/api/v1/detect

# 监控资源使用
docker stats test-algo
```

## 📦 部署到平台

### 1. 构建最终镜像
```bash
# 构建生产镜像
docker build -t your-algorithm:v1.0.0 .

# 标记为latest
docker tag your-algorithm:v1.0.0 your-algorithm:latest
```

### 2. 启动容器
```bash
# 启动算法容器
docker run -d \
  --name your-algorithm \
  -p 8001:8000 \
  --restart unless-stopped \
  your-algorithm:latest
```

### 3. 平台自动发现
算法管理平台会自动扫描并发现带有 `algorithm.platform="true"` 标签的容器。

### 4. 验证集成
1. 访问平台管理界面
2. 在"容器管理"中查看新算法
3. 在"在线测试"中测试算法功能
4. 在"系统监控"中查看运行状态

## 🔍 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看日志
   docker logs your-algorithm
   
   # 检查端口占用
   netstat -tulpn | grep 8000
   ```

2. **模型加载失败**
   - 检查模型文件路径和权限
   - 确认模型格式兼容性
   - 查看内存使用情况

3. **API响应慢**
   - 优化模型推理速度
   - 调整并发处理数量
   - 使用GPU加速

4. **平台无法发现算法**
   - 确认Docker标签正确
   - 检查容器网络连接
   - 验证API接口可访问

### 调试技巧

1. **启用调试模式**
   ```bash
   docker run -e ALGORITHM_DEBUG=true your-algorithm:latest
   ```

2. **交互式调试**
   ```bash
   docker run -it --entrypoint /bin/bash your-algorithm:latest
   ```

3. **查看详细日志**
   ```bash
   docker logs -f your-algorithm
   ```

## 📚 最佳实践

### 1. 代码组织
- 保持代码模块化
- 使用类型提示
- 添加详细注释
- 遵循PEP 8规范

### 2. 性能优化
- 模型预加载
- 批处理支持
- 内存管理
- 异步处理

### 3. 错误处理
- 优雅的错误处理
- 详细的错误信息
- 日志记录
- 监控告警

### 4. 安全考虑
- 输入验证
- 文件类型检查
- 资源限制
- 权限控制

## 🎉 完成集成

恭喜！你已经成功将算法集成到平台中。现在可以：

1. 通过平台界面管理算法
2. 使用在线测试功能验证算法
3. 监控算法运行状态
4. 通过API调用算法服务

如有问题，请参考：
- [API规范文档](ALGORITHM_API_SPECIFICATION.md)
- [模板项目](template/)
- [示例算法](renchefei/)
