# 🚀 算法包开发规范与指南

**版本**: v2.0.0  
**更新时间**: 2025-07-30  
**适用范围**: 所有新开发的算法包

## 📋 概述

本文档整合了算法API规范和开发模板，为算法开发者提供完整的开发指南。所有算法包必须遵循v2.0统一响应格式，确保平台的一致性和可维护性。

## 🏗️ 技术栈要求

### 必需技术栈
- **编程语言**: Python 3.11+
- **Web框架**: FastAPI + Uvicorn
- **包管理器**: uv (推荐)
- **数据验证**: Pydantic v2
- **容器化**: Docker

### 推荐依赖包
```toml
[project]
dependencies = [
    # Web框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    
    # 文件处理
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.1",
    
    # 图像处理
    "opencv-python>=4.8.0",
    "Pillow>=10.0.0",
    "numpy>=1.24.0",
    
    # 数据验证
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # 工具库
    "loguru>=0.7.2",
    "psutil>=5.9.0",
]
```

## 📁 项目结构规范

```
your_algorithm/
├── pyproject.toml              # 项目配置
├── uv.lock                     # 依赖锁定文件
├── Dockerfile                  # 容器配置
├── README.md                   # 算法说明
├── config.ini                  # 算法配置
├── src/                        # 源代码目录
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── api_server.py           # API服务器
│   ├── models/                 # 数据模型
│   │   ├── __init__.py
│   │   └── unified_models.py   # 统一响应模型
│   ├── core/                   # 核心算法
│   │   ├── __init__.py
│   │   └── inference_engine.py # 推理引擎
│   └── utils/                  # 工具函数
│       ├── __init__.py
│       └── helpers.py
├── models/                     # 模型文件目录
├── tests/                      # 测试文件
│   ├── __init__.py
│   └── test_api.py
└── static/                     # 静态文件
    └── results/                # 结果输出目录
```

## 🔧 核心API接口规范

### 必需端点

所有算法包必须实现以下端点：

| 端点 | 方法 | 功能 | 状态 |
|------|------|------|------|
| `/api/v1/health` | GET | 健康检查 | 必需 |
| `/api/v1/info` | GET | 算法信息 | 必需 |
| `/api/v1/detect` | POST | 主要功能接口 | 必需 |
| `/docs` | GET | API文档 | 自动生成 |

### 可选端点

| 端点 | 方法 | 功能 | 适用场景 |
|------|------|------|----------|
| `/api/v1/status` | GET | 运行状态 | 复杂算法 |
| `/api/v1/process/batch` | POST | 批量处理 | 支持批量的算法 |
| `/api/v1/compare` | POST | 比对功能 | 人脸识别等 |

## 📊 v2.0统一响应格式

### 成功响应结构

```json
{
  "success": true,
  "error": null,
  "data": {
    "detections": [
      {
        "bbox": [x_min, y_min, x_max, y_max],  // 数组格式
        "confidence": 0.95,
        "label": "person",                      // 统一字段名
        "class_id": 0,
        "center": [x, y],                       // 自动计算
        "width": 200,                           // 自动计算
        "height": 300,                          // 自动计算
        "attributes": {                         // 扩展属性
          "track_id": 15,
          "landmarks": [[x1, y1], [x2, y2]],
          "feature_vector": [0.1, 0.2, 0.3],
          "quality_score": 0.95,
          "custom_field": "custom_value"
        }
      }
    ],
    "summary": {
      "num_detections": 1,
      "class_counts": {"person": 1},
      "confidence_stats": {
        "min": 0.95, "max": 0.95, "avg": 0.95
      }
    }
  },
  "metadata": {
    "processing_time_ms": 45.2,
    "image_shape": [1080, 1920, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z",
    "model_info": {
      "name": "your_algorithm",
      "version": "2.0.0"
    },
    "hardware_info": {
      "device": "cpu",
      "memory_used_mb": 512.0
    }
  }
}
```

### 错误响应结构

```json
{
  "success": false,
  "error": {
    "code": "INVALID_FILE_FORMAT",
    "message": "不支持的文件格式",
    "details": "仅支持图片文件",
    "timestamp": "2025-07-30T12:55:00.123Z"
  },
  "data": null,
  "metadata": {
    "processing_time_ms": 12.5,
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

## 🎯 关键设计原则

### 1. 统一字段命名
- ✅ 使用 `label` 而不是 `class_name`
- ✅ 使用 `num_detections` 而不是 `total_detections`
- ✅ 使用 `class_counts` 而不是 `classes_detected`

### 2. 边界框格式
- ✅ 统一使用数组格式: `[x_min, y_min, x_max, y_max]`
- ✅ 自动计算几何信息: `center`, `width`, `height`

### 3. 扩展性设计
- ✅ 使用 `attributes` 字段存储算法特有数据
- ✅ 支持任意嵌套的JSON结构
- ✅ 保持向后兼容性

### 4. 元数据完整性
- ✅ 包含处理时间、图像信息、时间戳
- ✅ 提供模型和硬件信息
- ✅ 便于监控和调试

## 🔨 开发步骤

### 第一步：项目初始化

```bash
# 创建项目目录
mkdir your_algorithm && cd your_algorithm

# 初始化uv项目
uv init --python 3.11

# 创建目录结构
mkdir -p src/{models,core,utils,api} models tests static/results
```

### 第二步：配置项目文件

创建 `pyproject.toml`:
```toml
[project]
name = "your-algorithm"
version = "2.0.0"
description = "你的算法描述"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "opencv-python>=4.8.0",
    "Pillow>=10.0.0",
    "numpy>=1.24.0",
    "pydantic>=2.5.0",
    "loguru>=0.7.2",
]
```

### 第三步：实现统一数据模型

创建 `src/models/unified_models.py` (完整代码见附录A)

### 第四步：实现API服务器

创建 `src/api_server.py` (完整代码见附录B)

### 第五步：实现推理引擎

创建 `src/core/inference_engine.py`:
```python
class YourAlgorithmEngine:
    def __init__(self):
        # 初始化模型
        pass
    
    def predict(self, image, **kwargs):
        # 实现你的算法逻辑
        # 返回符合v2.0格式的结果
        pass
```

### 第六步：配置Docker

创建 `Dockerfile` (完整配置见附录C)

### 第七步：测试验证

```bash
# 安装依赖
uv sync

# 运行测试
uv run python tests/test_api.py

# 启动服务
uv run python src/api_server.py

# 构建Docker镜像
docker build -t your_algorithm:v2.0 .

# 运行容器
docker run -d -p 8000:8000 --name your-algorithm your_algorithm:v2.0
```

## 🧪 测试规范

### 必需测试项目

1. **数据模型测试**
   - 检测结果模型验证
   - 几何信息自动计算
   - 扩展属性支持

2. **API端点测试**
   - 健康检查响应
   - 主要功能接口
   - 错误处理机制

3. **响应格式测试**
   - 成功响应结构
   - 错误响应结构
   - 字段类型验证

### 测试脚本模板

```python
#!/usr/bin/env python3
"""
算法API测试脚本
"""

import requests
import json

def test_health_check():
    """测试健康检查"""
    response = requests.get("http://localhost:8000/api/v1/health")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    print("✅ 健康检查通过")

def test_detection_api():
    """测试检测API"""
    with open("test_image.jpg", "rb") as f:
        files = {"file": f}
        response = requests.post(
            "http://localhost:8000/api/v1/detect",
            files=files
        )
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "detections" in data["data"]
    print("✅ 检测API测试通过")

if __name__ == "__main__":
    test_health_check()
    test_detection_api()
    print("🎉 所有测试通过！")
```

## 📦 Docker配置规范

### 必需标签

```dockerfile
# 算法标识标签（必需）
LABEL algorithm.platform="true"
LABEL algorithm.name="你的算法名称"
LABEL algorithm.type="算法类型"
LABEL algorithm.version="2.0.0"
LABEL algorithm.description="算法功能描述"
```

### 标准配置

```dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl libgl1-mesa-glx libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install --no-cache-dir uv

# 复制并安装依赖
COPY pyproject.toml uv.lock ./
RUN uv sync --frozen --no-dev

# 复制源代码
COPY src/ ./src/
COPY models/ ./models/

# 创建非root用户
RUN useradd -m -u 1000 appuser && \
    chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 启动命令
CMD ["uv", "run", "python", "src/api_server.py"]
```

## 🚀 部署指南

### 本地开发

```bash
# 克隆或创建项目
git clone your_algorithm_repo
cd your_algorithm

# 安装依赖
uv sync

# 启动开发服务器
uv run python src/api_server.py
```

### Docker部署

```bash
# 构建镜像
docker build -t your_algorithm:v2.0 .

# 运行容器
docker run -d \
  --name your-algorithm \
  -p 8000:8000 \
  your_algorithm:v2.0

# 检查状态
docker ps
curl http://localhost:8000/api/v1/health
```

### 生产环境

```bash
# 使用Docker Compose
version: '3.8'
services:
  your-algorithm:
    image: your_algorithm:v2.0
    ports:
      - "8000:8000"
    environment:
      - LOG_LEVEL=INFO
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 📋 检查清单

在提交算法包之前，请确保：

### 代码质量
- [ ] 遵循v2.0统一响应格式
- [ ] 实现所有必需的API端点
- [ ] 包含完整的错误处理
- [ ] 代码注释清晰完整

### 测试验证
- [ ] 所有单元测试通过
- [ ] API端点功能正常
- [ ] Docker容器构建成功
- [ ] 健康检查正常响应

### 文档完整
- [ ] README.md说明清晰
- [ ] API文档自动生成
- [ ] 配置文件有注释
- [ ] 部署说明完整

### 性能要求
- [ ] 健康检查响应时间 < 100ms
- [ ] 主要功能响应时间合理
- [ ] 内存使用在可接受范围
- [ ] 容器启动时间 < 60s

## 📞 支持与反馈

- **完整规范**: `algorithms/ALGORITHM_API_SPECIFICATION.md`
- **项目状态**: `PROJECT_STATUS_SNAPSHOT.md`
- **快速参考**: `QUICK_REFERENCE.md`
- **技术支持**: 算法平台团队

---

## 📎 附录A: 统一数据模型模板

### src/models/unified_models.py

```python
"""
统一响应格式模型 - v2.0规范
"""

import json
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime


class Detection(BaseModel):
    """单个检测结果 - 符合v2.0统一规范"""
    # 必选字段
    bbox: List[float] = Field(..., description="边界框坐标 [x_min, y_min, x_max, y_max]")
    confidence: float = Field(..., ge=0.0, le=1.0, description="检测结果的置信度")
    label: str = Field(..., description="类别名称")
    class_id: Optional[int] = Field(None, description="类别的数字ID")

    # 可选的通用几何信息
    center: Optional[List[float]] = Field(None, description="中心点坐标 [x, y]")
    width: Optional[float] = Field(None, description="边界框宽度")
    height: Optional[float] = Field(None, description="边界框高度")

    # 扩展属性 - 用于容纳算法特有数据
    attributes: Optional[Dict[str, Any]] = Field(default_factory=dict, description="算法特有的扩展数据")

    def __init__(self, **data):
        super().__init__(**data)
        # 自动计算几何信息
        if self.bbox and len(self.bbox) == 4:
            x_min, y_min, x_max, y_max = self.bbox
            if self.center is None:
                self.center = [(x_min + x_max) / 2, (y_min + y_max) / 2]
            if self.width is None:
                self.width = x_max - x_min
            if self.height is None:
                self.height = y_max - y_min


class ConfidenceStats(BaseModel):
    """置信度统计"""
    min: float = Field(..., description="最小置信度")
    max: float = Field(..., description="最大置信度")
    avg: float = Field(..., description="平均置信度")


class DetectionSummary(BaseModel):
    """检测结果摘要 - v2.0格式"""
    num_detections: int = Field(..., description="检测到的对象总数")
    class_counts: Dict[str, int] = Field(..., description="各类别的数量统计")
    confidence_stats: Optional[ConfidenceStats] = Field(None, description="置信度统计")


class ModelInfo(BaseModel):
    """模型信息"""
    name: str = Field(..., description="模型名称")
    version: str = Field(..., description="模型版本")


class HardwareInfo(BaseModel):
    """硬件信息"""
    device: str = Field(..., description="计算设备")
    memory_used_mb: Optional[float] = Field(None, description="内存使用量(MB)")


class Metadata(BaseModel):
    """响应元数据"""
    processing_time_ms: float = Field(..., description="处理耗时(毫秒)")
    image_shape: List[int] = Field(..., description="图像尺寸 [height, width, channels]")
    timestamp_utc: str = Field(..., description="UTC时间戳")
    model_info: Optional[ModelInfo] = Field(None, description="模型信息")
    hardware_info: Optional[HardwareInfo] = Field(None, description="硬件信息")


class DetectionData(BaseModel):
    """检测数据部分"""
    detections: List[Detection] = Field(..., description="检测结果列表")
    summary: DetectionSummary = Field(..., description="结果摘要")
    output_files: Optional[Dict[str, str]] = Field(None, description="输出文件URL")


class ErrorInfo(BaseModel):
    """错误信息"""
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="用户友好的错误消息")
    details: Optional[str] = Field(None, description="详细错误信息")
    timestamp: str = Field(..., description="错误发生时间")


class UnifiedResponse(BaseModel):
    """统一响应格式 - v2.0规范"""
    success: bool = Field(..., description="是否成功执行")
    error: Optional[ErrorInfo] = Field(None, description="错误信息，成功时为null")
    data: Optional[Union[DetectionData, Dict[str, Any]]] = Field(None, description="核心业务数据，失败时为null")
    metadata: Metadata = Field(..., description="处理元数据和诊断信息")


# 工具函数
def create_success_response(
    data: Union[DetectionData, Dict[str, Any]],
    metadata: Metadata
) -> UnifiedResponse:
    """创建成功响应"""
    return UnifiedResponse(
        success=True,
        error=None,
        data=data,
        metadata=metadata
    )


def create_error_response(
    error_code: str,
    error_message: str,
    error_details: Optional[str] = None,
    metadata: Optional[Metadata] = None
) -> UnifiedResponse:
    """创建错误响应"""
    error_info = ErrorInfo(
        code=error_code,
        message=error_message,
        details=error_details,
        timestamp=datetime.utcnow().isoformat() + "Z"
    )

    if metadata is None:
        metadata = Metadata(
            processing_time_ms=0.0,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z"
        )

    return UnifiedResponse(
        success=False,
        error=error_info,
        data=None,
        metadata=metadata
    )
```

## 📎 附录B: API服务器模板

### src/api_server.py

```python
#!/usr/bin/env python3
"""
算法API服务器 - v2.0统一响应格式
"""

import time
from datetime import datetime
from typing import Optional
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from PIL import Image
import io
import numpy as np

# 导入统一响应模型
from models.unified_models import (
    UnifiedResponse,
    DetectionData,
    Metadata,
    ModelInfo,
    HardwareInfo,
    create_success_response,
    create_error_response
)

# 导入你的推理引擎
from core.inference_engine import YourAlgorithmEngine

# 创建FastAPI应用
app = FastAPI(
    title="你的算法API v2.0",
    description="算法功能描述 - 统一响应格式v2.0",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局推理引擎实例
engine: Optional[YourAlgorithmEngine] = None


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        engine = YourAlgorithmEngine()
        print("✅ 推理引擎初始化成功")
    except Exception as e:
        print(f"❌ 推理引擎初始化失败: {e}")


@app.get("/api/v1/health", response_model=UnifiedResponse)
async def health_check():
    """健康检查接口 - v2.0格式"""
    start_time = time.time()

    health_data = {
        "service": "你的算法名称",
        "status": "healthy",
        "engine_loaded": engine is not None
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="your_algorithm",
            version="2.0.0"
        ),
        hardware_info=HardwareInfo(
            device="cpu"
        )
    )

    return create_success_response(
        data=health_data,
        metadata=metadata
    )


@app.get("/api/v1/info", response_model=UnifiedResponse)
async def get_algorithm_info():
    """获取算法信息 - v2.0格式"""
    start_time = time.time()

    algorithm_info = {
        "algorithm_name": "你的算法名称",
        "algorithm_version": "2.0.0",
        "algorithm_type": "目标检测",  # 根据实际情况修改
        "description": "算法功能详细描述",
        "capabilities": {
            "input_modes": ["file"],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"],
            "max_file_size_mb": 50,
            "concurrent_requests": 5
        },
        "model_info": {
            "framework": "PyTorch",  # 根据实际情况修改
            "model_file": "your_model.pt",
            "input_size": [640, 640],
            "classes": ["class1", "class2"]  # 根据实际情况修改
        }
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="your_algorithm",
            version="2.0.0"
        )
    )

    return create_success_response(
        data=algorithm_info,
        metadata=metadata
    )


@app.post("/api/v1/detect", response_model=UnifiedResponse)
async def detect_objects(
    file: UploadFile = File(...),
    confidence_threshold: float = Form(default=0.5)
):
    """主要检测接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 验证文件类型
        if not file.content_type.startswith('image/'):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件格式",
                error_details="仅支持图片文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 读取图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        image_array = np.array(image)

        # 执行推理
        detection_data = engine.predict(
            image_array,
            confidence_threshold=confidence_threshold
        )

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=[image_array.shape[0], image_array.shape[1], image_array.shape[2] if len(image_array.shape) > 2 else 3],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="your_algorithm",
                version="2.0.0"
            ),
            hardware_info=HardwareInfo(
                device="cpu"
            )
        )

        return create_success_response(
            data=detection_data,
            metadata=metadata
        )

    except Exception as e:
        return create_error_response(
            error_code="DETECTION_ERROR",
            error_message="检测处理失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


if __name__ == "__main__":
    import uvicorn
    print("启动算法API服务器 v2.0")
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
```

## 📎 附录C: Docker配置模板

### Dockerfile

```dockerfile
# 算法容器 - 基于统一API规范v2.0
FROM python:3.11-slim

# 设置算法标签（必需）
LABEL algorithm.platform="true"
LABEL algorithm.name="你的算法名称"
LABEL algorithm.type="目标检测"
LABEL algorithm.version="2.0.0"
LABEL algorithm.description="算法功能描述，采用统一响应格式v2.0"

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install --no-cache-dir uv

# 复制项目配置文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN uv sync --frozen --no-dev

# 复制源代码和模型
COPY src/ ./src/
COPY models/ ./models/
COPY config/ ./config/

# 创建结果目录
RUN mkdir -p /app/static/results

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 启动命令
CMD ["uv", "run", "python", "src/api_server.py"]
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  your-algorithm:
    build: .
    image: your_algorithm:v2.0
    container_name: your-algorithm-v2
    ports:
      - "8000:8000"
    environment:
      - LOG_LEVEL=INFO
      - MODEL_PATH=/app/models
    volumes:
      - ./static/results:/app/static/results
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

## 📎 附录D: 推理引擎模板

### src/core/inference_engine.py

```python
"""
推理引擎模板 - 实现你的算法逻辑
"""

import numpy as np
from typing import List, Dict, Any, Optional
from models.unified_models import Detection, DetectionData, DetectionSummary, ConfidenceStats


class YourAlgorithmEngine:
    """你的算法推理引擎"""

    def __init__(self, model_path: str = "models/your_model.pt"):
        """初始化推理引擎"""
        self.model_path = model_path
        self.model = None
        self.class_names = ["class1", "class2", "class3"]  # 根据实际情况修改
        self._load_model()

    def _load_model(self):
        """加载模型"""
        try:
            # 在这里实现你的模型加载逻辑
            # 例如：
            # import torch
            # self.model = torch.load(self.model_path)
            # self.model.eval()

            print(f"✅ 模型加载成功: {self.model_path}")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise

    def predict(
        self,
        image: np.ndarray,
        confidence_threshold: float = 0.5,
        **kwargs
    ) -> DetectionData:
        """
        执行推理预测

        Args:
            image: 输入图像 (numpy数组)
            confidence_threshold: 置信度阈值
            **kwargs: 其他参数

        Returns:
            DetectionData: 符合v2.0格式的检测结果
        """
        try:
            # 在这里实现你的推理逻辑
            detections = self._run_inference(image, confidence_threshold)

            # 计算置信度统计
            confidences = [d.confidence for d in detections]
            confidence_stats = None
            if confidences:
                confidence_stats = ConfidenceStats(
                    min=min(confidences),
                    max=max(confidences),
                    avg=sum(confidences) / len(confidences)
                )

            # 统计类别数量
            class_counts = {}
            for detection in detections:
                class_counts[detection.label] = class_counts.get(detection.label, 0) + 1

            # 创建摘要
            summary = DetectionSummary(
                num_detections=len(detections),
                class_counts=class_counts,
                confidence_stats=confidence_stats
            )

            return DetectionData(
                detections=detections,
                summary=summary
            )

        except Exception as e:
            print(f"推理失败: {e}")
            raise

    def _run_inference(self, image: np.ndarray, confidence_threshold: float) -> List[Detection]:
        """
        运行模型推理 - 需要根据你的算法实现

        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值

        Returns:
            List[Detection]: 检测结果列表
        """
        detections = []

        # 示例：模拟检测结果
        # 在实际实现中，这里应该是你的模型推理代码

        # 模拟检测到一个对象
        detection = Detection(
            bbox=[100, 100, 200, 200],  # [x_min, y_min, x_max, y_max]
            confidence=0.95,
            label="example_class",
            class_id=0,
            attributes={
                "example_attribute": "example_value",
                "quality_score": 0.9
            }
        )
        detections.append(detection)

        # 过滤低置信度的检测结果
        detections = [d for d in detections if d.confidence >= confidence_threshold]

        return detections

    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理

        Args:
            image: 原始图像

        Returns:
            np.ndarray: 预处理后的图像
        """
        # 在这里实现你的图像预处理逻辑
        # 例如：调整大小、归一化等
        return image

    def postprocess_results(self, raw_results: Any) -> List[Detection]:
        """
        后处理模型输出

        Args:
            raw_results: 模型原始输出

        Returns:
            List[Detection]: 处理后的检测结果
        """
        # 在这里实现你的后处理逻辑
        # 例如：NMS、坐标转换等
        detections = []
        return detections
```

## 📎 附录E: 测试脚本模板

### tests/test_api.py

```python
#!/usr/bin/env python3
"""
算法API完整测试脚本
"""

import requests
import json
import time
from pathlib import Path


class AlgorithmAPITester:
    """算法API测试器"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()

    def test_health_check(self):
        """测试健康检查"""
        print("🧪 测试健康检查...")

        response = self.session.get(f"{self.base_url}/api/v1/health")
        assert response.status_code == 200

        data = response.json()
        assert data["success"] is True
        assert data["error"] is None
        assert "data" in data
        assert "metadata" in data

        print("✅ 健康检查测试通过")
        return data

    def test_algorithm_info(self):
        """测试算法信息"""
        print("🧪 测试算法信息...")

        response = self.session.get(f"{self.base_url}/api/v1/info")
        assert response.status_code == 200

        data = response.json()
        assert data["success"] is True
        assert "algorithm_name" in data["data"]
        assert "algorithm_version" in data["data"]

        print("✅ 算法信息测试通过")
        return data

    def test_detection_api(self, image_path: str = "test_image.jpg"):
        """测试检测API"""
        print("🧪 测试检测API...")

        # 检查测试图片是否存在
        if not Path(image_path).exists():
            print(f"⚠️ 测试图片不存在: {image_path}")
            return None

        with open(image_path, "rb") as f:
            files = {"file": f}
            data = {"confidence_threshold": 0.5}

            response = self.session.post(
                f"{self.base_url}/api/v1/detect",
                files=files,
                data=data
            )

        assert response.status_code == 200

        result = response.json()
        assert result["success"] is True
        assert "detections" in result["data"]
        assert "summary" in result["data"]
        assert "metadata" in result

        print("✅ 检测API测试通过")
        return result

    def test_error_handling(self):
        """测试错误处理"""
        print("🧪 测试错误处理...")

        # 测试无效文件格式
        files = {"file": ("test.txt", b"invalid content", "text/plain")}
        response = self.session.post(
            f"{self.base_url}/api/v1/detect",
            files=files
        )

        data = response.json()
        assert data["success"] is False
        assert data["error"] is not None
        assert data["data"] is None

        print("✅ 错误处理测试通过")
        return data

    def test_response_format(self, response_data: dict):
        """测试响应格式"""
        print("🧪 测试响应格式...")

        # 检查顶层结构
        required_fields = ["success", "error", "data", "metadata"]
        for field in required_fields:
            assert field in response_data, f"缺少必需字段: {field}"

        # 检查元数据结构
        metadata = response_data["metadata"]
        metadata_fields = ["processing_time_ms", "timestamp_utc"]
        for field in metadata_fields:
            assert field in metadata, f"元数据缺少字段: {field}"

        print("✅ 响应格式测试通过")

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行算法API测试套件\n")

        try:
            # 基础功能测试
            health_data = self.test_health_check()
            info_data = self.test_algorithm_info()

            # 响应格式测试
            self.test_response_format(health_data)
            self.test_response_format(info_data)

            # 检测功能测试
            detection_data = self.test_detection_api()
            if detection_data:
                self.test_response_format(detection_data)

            # 错误处理测试
            error_data = self.test_error_handling()
            self.test_response_format(error_data)

            print("\n🎉 所有测试通过！算法API符合v2.0规范")

        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            raise


def main():
    """主函数"""
    tester = AlgorithmAPITester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
```

---

**开发规范完成**: 本文档提供了完整的算法包开发指南，包含所有必需的代码模板和配置示例。请严格遵循v2.0统一响应格式规范进行开发。
