#!/usr/bin/env python3
"""
人车非检测算法 - API服务器
提供RESTful API接口用于人车非目标检测
"""

import os
import io
import time
from typing import List, Optional
import configparser

import uvicorn
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from PIL import Image
import cv2

# 导入推理引擎
from inference_engine import RenchefeiDetectionEngine
from logger_config import get_logger

# 初始化日志
logger = get_logger()

# 创建FastAPI应用
app = FastAPI(
    title="人车非检测算法API",
    description="基于YOLOv5的人车非目标检测API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局推理引擎实例
engine: Optional[RenchefeiDetectionEngine] = None


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[dict] = None
    error: Optional[str] = None
    timestamp: str
    processing_time: Optional[float] = None


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_file = "config.ini"
    
    try:
        config.read(config_file, encoding='utf-8')
        conf_thres = config.getfloat('DETECTION', 'confidence_threshold', fallback=0.25)
        iou_thres = config.getfloat('DETECTION', 'iou_threshold', fallback=0.45)
        max_det = config.getint('DETECTION', 'max_detections', fallback=1000)
        return conf_thres, iou_thres, max_det
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return 0.25, 0.45, 1000


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        logger.info("正在初始化人车非检测引擎...")
        engine = RenchefeiDetectionEngine()
        
        # 加载配置参数
        conf_thres, iou_thres, max_det = load_config()
        engine.set_detection_params(conf_thres=conf_thres, iou_thres=iou_thres, max_det=max_det)
        
        logger.info("人车非检测引擎初始化完成")
    except Exception as e:
        logger.error(f"推理引擎初始化失败: {e}")
        raise e


@app.get("/api/v1/health")
async def health_check():
    """健康检查接口"""
    return APIResponse(
        success=True,
        message="服务运行正常",
        data={
            "service": "人车非检测算法",
            "status": "healthy",
            "engine_loaded": engine is not None
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.get("/api/v1/info")
async def get_algorithm_info():
    """获取算法信息"""
    return APIResponse(
        success=True,
        message="算法信息获取成功",
        data={
            "name": "人车非检测算法",
            "version": "1.0.0",
            "description": "基于YOLOv5的人车非目标检测算法",
            "capabilities": [
                "人员检测",
                "车辆检测", 
                "非机动车检测",
                "车牌检测",
                "人头检测",
                "跌倒检测"
            ],
            "classes": ["person", "vehicle", "bicycle", "plate", "head", "fall"],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"]
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.post("/api/v1/detect")
async def detect_objects(
    file: UploadFile = File(...),
    conf_threshold: float = Form(default=None),
    iou_threshold: float = Form(default=None)
):
    """目标检测接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")
    
    start_time = time.time()
    
    try:
        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为OpenCV格式
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # 执行推理
        results = engine.predict(
            image_cv,
            conf_thres=conf_threshold,
            iou_thres=iou_threshold
        )
        
        processing_time = time.time() - start_time
        
        return APIResponse(
            success=True,
            message=f"检测完成，发现 {results['num_detections']} 个目标",
            data=results,
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(f"目标检测失败: {e}")
        return APIResponse(
            success=False,
            message="目标检测失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/detect_batch")
async def detect_batch(files: List[UploadFile] = File(...)):
    """批量检测接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()
    results = []

    try:
        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

                # 执行推理
                result = engine.predict(image_cv)
                result['filename'] = file.filename
                result['index'] = i
                results.append(result)

            except Exception as e:
                results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'num_detections': 0,
                    'detections': []
                })

        processing_time = time.time() - start_time
        total_detections = sum(r.get('num_detections', 0) for r in results)

        return APIResponse(
            success=True,
            message=f"批量检测完成，处理 {len(files)} 张图像，共发现 {total_detections} 个目标",
            data={
                "total_images": len(files),
                "total_detections": total_detections,
                "results": results
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"批量检测失败: {e}")
        return APIResponse(
            success=False,
            message="批量检测失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/detect_by_class")
async def detect_by_class(
    file: UploadFile = File(...),
    target_classes: List[str] = Form(default=None),
    conf_threshold: float = Form(default=None)
):
    """按类别检测接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()

    try:
        # 读取图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # 执行推理
        results = engine.predict(image_cv, conf_thres=conf_threshold)

        # 如果指定了目标类别，过滤结果
        if target_classes:
            filtered_detections = []
            for detection in results['detections']:
                if detection['class_name'] in target_classes:
                    filtered_detections.append(detection)

            results['detections'] = filtered_detections
            results['num_detections'] = len(filtered_detections)

            # 重新统计类别数量
            results['class_counts'] = {}
            for detection in filtered_detections:
                class_name = detection['class_name']
                results['class_counts'][class_name] = results['class_counts'].get(class_name, 0) + 1

        processing_time = time.time() - start_time
        results['processing_time'] = processing_time

        filter_msg = f"，过滤类别: {target_classes}" if target_classes else ""

        return APIResponse(
            success=True,
            message=f"类别检测完成，发现 {results['num_detections']} 个目标{filter_msg}",
            data=results,
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"类别检测失败: {e}")
        return APIResponse(
            success=False,
            message="类别检测失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.get("/api/v1/classes")
async def get_supported_classes():
    """获取支持的检测类别"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    return APIResponse(
        success=True,
        message="获取支持的类别成功",
        data={
            "classes": engine.class_names,
            "num_classes": len(engine.class_names),
            "class_descriptions": {
                "person": "人员检测",
                "vehicle": "机动车辆检测",
                "bicycle": "自行车/非机动车检测",
                "plate": "车牌检测",
                "head": "人头检测",
                "fall": "跌倒行为检测"
            }
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.post("/api/v1/statistics")
async def get_detection_statistics(files: List[UploadFile] = File(...)):
    """检测统计分析接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()

    try:
        total_stats = {
            'total_images': len(files),
            'total_detections': 0,
            'class_statistics': {},
            'confidence_distribution': {},
            'processing_times': []
        }

        for file in files:
            # 读取图像
            image_data = await file.read()
            image = Image.open(io.BytesIO(image_data))
            image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            # 执行检测
            result = engine.predict(image_cv)
            total_stats['processing_times'].append(result['processing_time'])
            total_stats['total_detections'] += result['num_detections']

            # 统计类别
            for class_name, count in result['class_counts'].items():
                if class_name not in total_stats['class_statistics']:
                    total_stats['class_statistics'][class_name] = {
                        'count': 0,
                        'images_with_class': 0,
                        'avg_confidence': 0,
                        'confidences': []
                    }

                total_stats['class_statistics'][class_name]['count'] += count
                total_stats['class_statistics'][class_name]['images_with_class'] += 1

                # 收集置信度
                for detection in result['detections']:
                    if detection['class_name'] == class_name:
                        total_stats['class_statistics'][class_name]['confidences'].append(detection['confidence'])

        # 计算平均置信度
        for class_name, stats in total_stats['class_statistics'].items():
            if stats['confidences']:
                stats['avg_confidence'] = sum(stats['confidences']) / len(stats['confidences'])
                stats['min_confidence'] = min(stats['confidences'])
                stats['max_confidence'] = max(stats['confidences'])
                del stats['confidences']  # 删除原始数据以减少响应大小

        # 计算处理时间统计
        processing_times = total_stats['processing_times']
        total_stats['performance'] = {
            'avg_processing_time': sum(processing_times) / len(processing_times),
            'min_processing_time': min(processing_times),
            'max_processing_time': max(processing_times),
            'total_processing_time': sum(processing_times)
        }
        del total_stats['processing_times']

        processing_time = time.time() - start_time

        return APIResponse(
            success=True,
            message=f"统计分析完成，处理 {len(files)} 张图像",
            data=total_stats,
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"统计分析失败: {e}")
        return APIResponse(
            success=False,
            message="统计分析失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


if __name__ == "__main__":
    # 启动API服务器
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
