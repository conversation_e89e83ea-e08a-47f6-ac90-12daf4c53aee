# 统一AI算法API规范

## 📋 概述

此规范定义了每个独立AI算法包（作为Docker容器运行）必须对外提供的标准API接口。每个算法容器都应启动一个Web服务来暴露这些端点，确保算法管理平台能够统一管理和调用各种算法。

## 🏗️ 基础约定

### 技术栈推荐
- **Web框架**: Python FastAPI（推荐）
  - 自动生成OpenAPI文档
  - 原生异步支持
  - 强类型校验
  - 高性能
- **包管理**: uv（推荐）
  - 极快的依赖解析和安装
  - 兼容pip和pyproject.toml
  - 内置虚拟环境管理
  - 锁文件支持

### API设计原则
- **Base URL**: 所有API路径以 `/api/v1` 开头，便于版本管理
- **数据格式**: 请求体和响应体均使用JSON格式
- **状态码**: 严格遵守HTTP状态码语义
- **错误处理**: 统一的错误响应格式
- **文档**: 每个算法必须提供OpenAPI文档

### Docker容器要求
```dockerfile
# 必需标签
LABEL algorithm.platform="true"
LABEL algorithm.name="算法名称"
LABEL algorithm.type="算法类型"
LABEL algorithm.version="1.0.0"
LABEL algorithm.description="算法描述"

# 标准端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1
```

## 🔧 核心API接口

### 1. 管理与自省API

#### 1.1 健康检查
```http
GET /api/v1/health
```

**用途**: 容器健康检查，供Docker/K8s进行存活探针

**响应**:
```json
{
  "status": "ok",
  "timestamp": "2025-07-29T10:30:00.123Z"
}
```

#### 1.2 算法信息
```http
GET /api/v1/info
```

**用途**: 获取算法元信息，供平台展示算法详情

**响应**:
```json
{
  "algorithm_name": "人车非检测算法",
  "algorithm_version": "1.0.0",
  "algorithm_type": "目标检测",
  "description": "基于YOLOv8的人车非目标检测算法，支持实时检测和批量处理",
  "capabilities": {
    "input_modes": ["file", "stream", "batch"],
    "supported_formats": ["jpg", "jpeg", "png", "mp4", "avi", "rtsp"],
    "max_file_size_mb": 100,
    "concurrent_requests": 5
  },
  "model_info": {
    "framework": "PyTorch",
    "model_file": "yolov8n.pt",
    "input_size": [640, 640],
    "classes": ["person", "car", "bicycle", "motorcycle"]
  },
  "performance": {
    "avg_inference_time_ms": 50,
    "throughput_fps": 20,
    "memory_usage_mb": 512
  }
}
```

#### 1.3 运行状态
```http
GET /api/v1/status
```

**用途**: 获取算法当前运行状态

**响应**:
```json
{
  "status": "ready",
  "message": "模型已加载，准备接受任务",
  "uptime_seconds": 3600,
  "current_load": {
    "active_tasks": 2,
    "queue_length": 0,
    "cpu_percent": 15.5,
    "memory_used_mb": 1024,
    "gpu_memory_used_mb": 2048,
    "gpu_memory_total_mb": 8192
  },
  "statistics": {
    "total_requests": 1250,
    "successful_requests": 1200,
    "failed_requests": 50,
    "avg_response_time_ms": 45
  }
}
```

**状态值说明**:
- `ready`: 就绪，可接受请求
- `busy`: 繁忙，但仍可接受请求
- `loading`: 模型加载中
- `error`: 错误状态
- `maintenance`: 维护模式

### 2. 文件处理API

#### 2.1 同步处理（快速响应）
```http
POST /api/v1/detect
```

**用途**: 同步处理单个文件，适用于快速检测场景

**请求**: `multipart/form-data`
- `file`: 图片或视频文件
- `parameters`: JSON字符串（可选）

**参数示例**:
```json
{
  "confidence_threshold": 0.5,
  "iou_threshold": 0.4,
  "max_detections": 100,
  "return_crops": false,
  "annotate_image": true
}
```

**成功响应** (200 OK):
```json
{
  "success": true,
  "processing_time_ms": 45,
  "results": {
    "detections": [
      {
        "class_id": 0,
        "class_name": "person",
        "confidence": 0.92,
        "bbox": {
          "x_min": 100,
          "y_min": 150,
          "x_max": 250,
          "y_max": 300,
          "width": 150,
          "height": 150
        },
        "center": {
          "x": 175,
          "y": 225
        }
      }
    ],
    "summary": {
      "total_detections": 1,
      "classes_detected": ["person"],
      "image_size": {
        "width": 1920,
        "height": 1080
      }
    }
  },
  "output_files": {
    "annotated_image": "/api/v1/static/results/annotated_12345.jpg"
  }
}
```

#### 2.2 异步处理（长时间任务）
```http
POST /api/v1/process/async
```

**用途**: 异步处理大文件或批量任务

**请求**: 同上

**成功响应** (202 Accepted):
```json
{
  "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status": "pending",
  "message": "任务已接收，正在排队处理",
  "estimated_time_seconds": 120
}
```

#### 2.3 查询异步任务结果
```http
GET /api/v1/results/{job_id}
```

**处理中响应**:
```json
{
  "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status": "processing",
  "progress_percent": 45,
  "message": "正在处理第 45/100 帧",
  "estimated_remaining_seconds": 60
}
```

**完成响应**:
```json
{
  "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status": "completed",
  "processing_time_seconds": 95,
  "results": {
    "detections": [...],
    "summary": {...}
  },
  "output_files": {
    "annotated_video": "/api/v1/static/results/output_12345.mp4",
    "detection_log": "/api/v1/static/results/detections_12345.json"
  }
}
```

### 3. 批量处理API

#### 3.1 批量文件处理
```http
POST /api/v1/process/batch
```

**用途**: 批量处理多个文件

**请求**: `multipart/form-data`
- `files`: 多个文件
- `parameters`: 统一参数

**响应**:
```json
{
  "batch_id": "batch_12345",
  "total_files": 10,
  "jobs": [
    {
      "file_name": "image1.jpg",
      "job_id": "job_001",
      "status": "pending"
    }
  ]
}
```

### 4. 实时流处理API

#### 4.1 WebSocket流处理
```
WS /api/v1/stream
```

**连接建立后发送启动指令**:
```json
{
  "action": "start",
  "stream_config": {
    "source_type": "rtsp",
    "source_url": "rtsp://user:pass@*************/stream",
    "fps": 25,
    "resolution": "1920x1080"
  },
  "parameters": {
    "confidence_threshold": 0.6,
    "send_interval_frames": 5,
    "roi_areas": [
      {
        "name": "entrance",
        "polygon": [[100, 100], [500, 100], [500, 400], [100, 400]]
      }
    ]
  }
}
```

**实时检测结果推送**:
```json
{
  "type": "detection",
  "timestamp": "2025-07-29T10:30:00.123Z",
  "frame_id": 1250,
  "detections": [
    {
      "track_id": 15,
      "class_name": "person",
      "confidence": 0.88,
      "bbox": {...},
      "roi_area": "entrance"
    }
  ],
  "stream_info": {
    "fps": 24.5,
    "frame_count": 1250,
    "dropped_frames": 2
  }
}
```

### 5. 静态文件服务

#### 5.1 结果文件下载
```http
GET /api/v1/static/results/{filename}
```

**用途**: 下载处理结果文件（标注图片、视频等）

### 6. 模型管理API

#### 6.1 模型列表
```http
GET /api/v1/models
```

**响应**:
```json
{
  "models": [
    {
      "model_id": "yolov8n",
      "name": "YOLOv8 Nano",
      "version": "1.0.0",
      "status": "loaded",
      "accuracy": 0.85,
      "speed_ms": 20
    }
  ],
  "active_model": "yolov8n"
}
```

#### 6.2 切换模型
```http
POST /api/v1/models/{model_id}/activate
```

## 🚨 错误处理

### 统一错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_FILE_FORMAT",
    "message": "不支持的文件格式",
    "details": "支持的格式: jpg, png, mp4",
    "timestamp": "2025-07-29T10:30:00.123Z"
  }
}
```

### 常见错误码
- `INVALID_FILE_FORMAT`: 不支持的文件格式
- `FILE_TOO_LARGE`: 文件过大
- `MODEL_NOT_LOADED`: 模型未加载
- `INSUFFICIENT_RESOURCES`: 资源不足
- `PROCESSING_TIMEOUT`: 处理超时
- `INVALID_PARAMETERS`: 参数错误

## 📊 性能要求

### 响应时间要求
- 健康检查: < 100ms
- 同步检测: < 5s
- 状态查询: < 200ms
- 异步任务提交: < 500ms

### 并发要求
- 最少支持5个并发请求
- 队列长度至少50个任务
- 优雅处理资源不足情况

## 🔍 监控与日志

### 必需的监控指标
- 请求总数、成功率、失败率
- 平均响应时间、P95响应时间
- 当前活跃连接数
- 资源使用情况（CPU、内存、GPU）
- 模型推理时间

### 日志格式
```json
{
  "timestamp": "2025-07-29T10:30:00.123Z",
  "level": "INFO",
  "component": "detector",
  "message": "Processing completed",
  "job_id": "12345",
  "processing_time_ms": 45,
  "detections_count": 3
}
```

## 📝 实现示例

参考实现请查看：
- `algorithms/renchefei/` - 人车非检测算法实现示例
- `algorithms/template/` - 算法模板项目

## 🔄 版本管理

- API版本通过URL路径管理（/api/v1, /api/v2）
- 向后兼容性要求
- 废弃API的迁移指南
