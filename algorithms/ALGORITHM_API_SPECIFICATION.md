# 🚀 算法平台API规范

**版本**: v2.0.0
**更新时间**: 2025-07-30
**适用范围**: 所有算法容器

> **📖 完整开发指南**: 请参阅 [`ALGORITHM_DEVELOPMENT_GUIDE.md`](./ALGORITHM_DEVELOPMENT_GUIDE.md) 获取详细的开发规范、代码模板和实施指南。

## 目录
1. [概述](#1-概述)
2. [核心原则与约定](#2-核心原则与约定)
3. [统一响应模型](#3-统一响应模型)
4. [API接口详解](#4-api接口详解)
5. [推荐项目结构](#5-推荐项目结构)
6. [附录A: 项目状态与开发计划](#附录a-项目状态与开发计划)
7. [附录B: v1.0 -> v2.0 迁移指南](#附录b-v10---v20-迁移指南)

---

## 1. 概述

本规范定义了所有AI算法包作为Docker容器运行时，必须对外提供的标准API接口。其目标是确保任何算法都能以“即插即用”的方式无缝接入AI管理平台。

- **目标读者**: 算法开发者、平台后端工程师、测试工程师。
- **核心价值**: 实现算法的统一管理、调用、测试与监控。

### 📚 相关文档
- **开发规范**: [`ALGORITHM_DEVELOPMENT_GUIDE.md`](./ALGORITHM_DEVELOPMENT_GUIDE.md) - 完整的算法包开发指南
- **项目状态**: [`../PROJECT_STATUS_SNAPSHOT.md`](../PROJECT_STATUS_SNAPSHOT.md) - 当前项目状态快照
- **快速参考**: [`../QUICK_REFERENCE.md`](../QUICK_REFERENCE.md) - 常用命令和API速查

## 2. 核心原则与约定

### 2.1 技术栈 (Technology Stack)
- **Web框架**: **Python FastAPI** (推荐)
- **包管理**: **uv** (推荐)
- **容器化**: **Docker**

### 2.2 API设计 (API Design)
- **协议**: RESTful & WebSocket
- **基路径 (Base URL)**: `/api/v1`
- **数据格式**: 请求体与响应体均为 **JSON**
- **认证**: (待定) 推荐通过 `X-API-Key` Header进行认证

### 2.3 容器化要求 (Containerization)

`Dockerfile`应遵循以下最佳实践：

dockerfile
# 1. 使用官方推荐的Python基础镜像
FROM python:3.11-slim

# 2. 设置工作目录
WORKDIR /app

# 3. 使用uv进行高效的依赖安装
RUN pip install --no-cache-dir uv
COPY pyproject.toml uv.lock* ./
# --frozen确保使用锁文件版本，--no-dev跳过开发依赖
RUN uv pip sync --frozen --no-dev

# 4. 复制源代码和模型文件
COPY ./src ./src
COPY ./models ./models

# 5. 添加平台识别标签
LABEL algorithm.platform="true" \
      algorithm.name="人车非检测" \
      algorithm.version="2.0.0"

# 6. 暴露标准端口
EXPOSE 8000

# 7. 设置健康检查
HEALTHCHECK --interval=30s --timeout=5s --retries=3 \
  CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 8. 定义启动命令
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
Use code with caution.
Markdown
3. 统一响应模型
所有API响应都必须封装在以下结构中。
3.1 基础结构
字段	类型	描述
success	boolean	必须。true表示成功，false表示失败。
data	object | null	必须。成功时包含业务数据，失败时为null。
error	object | null	必须。失败时包含错误对象，成功时为null。
metadata	object	必须。包含处理耗时、时间戳等诊断信息。
3.2 黄金模板：检测类算法 (Detection)
这是所有检测类算法（目标、人脸、关键点等）必须遵循的data结构。
Generated json
{
  "detections": [
    {
      "bbox":,
      "confidence": 0.8829,
      "label": "face",
      "class_id": 0,
      "attributes": {
        "landmarks": [,, ... ],
        "track_id": 15,
        "feature_vector": [ 0.0458, -0.0261, ... ]
      }
    }
  ],
  "summary": {
    "num_detections": 1,
    "class_counts": { "face": 1 }
  }
}
Use code with caution.
Json
bbox: [x_min, y_min, x_max, y_max]，标准边界框。
label: 统一使用label作为类别名称。
attributes: 一个灵活的对象，用于存放任何算法特有的数据（如关键点、特征向量、跟踪ID等），是保证规范可扩展性的关键。
3.3 错误响应 (Error)
当 success 为 false 时，error 字段必须包含以下内容：
Generated json
{
  "code": "INVALID_FILE_FORMAT",
  "message": "不支持的文件格式，请上传 jpg, png, mp4",
  "details": "MIME type 'application/pdf' is not supported."
}
Use code with caution.
Json
4. API接口详解
4.1 管理接口 (Management)
GET /api/v1/health: 健康检查，返回 {"status": "ok"}。
GET /api/v1/info: 获取算法元信息（名称、版本、能力等）。
GET /api/v1/status: 获取算法实时运行状态（负载、任务队列等）。
4.2 推理接口 (Inference)
同步处理 (适用于快速任务)
POST /api/v1/detect
用途: 处理单张图片或短视频。
请求: multipart/form-data 包含 file 和可选的 parameters (JSON字符串)。
响应: 返回包含检测结果的统一响应模型。
异步处理 (适用于长耗时任务)
POST /api/v1/process/async
用途: 提交一个长耗时任务（如长视频分析）。
响应: 202 Accepted，返回一个包含 job_id 的JSON。
GET /api/v1/results/{job_id}
用途: 查询异步任务的状态和最终结果。
实时流处理
WS /api/v1/stream
用途: 通过WebSocket处理实时视频流（如RTSP）。
流程:
客户端发起连接。
客户端发送 start 指令，包含 stream_url 和 parameters。
服务端持续推送包含检测结果的统一响应模型JSON消息。
客户端发送 stop 指令结束。
5. 推荐项目结构
Generated code
your-algorithm/
├── pyproject.toml         # 项目元数据与依赖 (PEP 621)
├── uv.lock                # 精确版本锁
├── Dockerfile             # 容器构建文件
├── README.md              # 项目说明
└── src/                   # 核心源代码
    ├── __init__.py
    ├── main.py            # FastAPI 应用实例 (app)
    ├── api/               # API路由模块
    ├── core/              # 核心算法逻辑
    └── models/            # Pydantic数据模型 (核心)