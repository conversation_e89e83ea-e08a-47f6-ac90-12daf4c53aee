# 统一AI算法API规范

## 📋 概述

此规范定义了每个独立AI算法包（作为Docker容器运行）必须对外提供的标准API接口。每个算法容器都应启动一个Web服务来暴露这些端点，确保算法管理平台能够统一管理和调用各种算法。

## 🏗️ 基础约定

### 技术栈推荐
- **Web框架**: Python FastAPI（推荐）
  - 自动生成OpenAPI文档
  - 原生异步支持
  - 强类型校验
  - 高性能
- **包管理**: uv（推荐）
  - 极快的依赖解析和安装
  - 兼容pip和pyproject.toml
  - 内置虚拟环境管理
  - 锁文件支持

### API设计原则
- **Base URL**: 所有API路径以 `/api/v1` 开头，便于版本管理
- **数据格式**: 请求体和响应体均使用JSON格式
- **状态码**: 严格遵守HTTP状态码语义
- **错误处理**: 统一的错误响应格式
- **文档**: 每个算法必须提供OpenAPI文档

### Docker容器要求
```dockerfile
# 必需标签
LABEL algorithm.platform="true"
LABEL algorithm.name="算法名称"
LABEL algorithm.type="算法类型"
LABEL algorithm.version="1.0.0"
LABEL algorithm.description="算法描述"

# 使用uv包管理器
RUN pip install --no-cache-dir uv
COPY pyproject.toml uv.lock ./
RUN uv sync --frozen --no-dev

# 标准端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 启动命令
CMD ["uv", "run", "python", "-m", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 📋 统一响应格式规范

### 响应结构标准

所有算法API的响应都必须遵循以下统一格式，确保平台能够统一处理各种算法的输出结果。

#### 基础响应结构

```json
{
  "success": true,        // 布尔值，表示任务是否成功执行
  "error": null,          // 如果 success 为 false，这里应包含错误信息对象
  "data": {
    // 核心业务数据，具体内容根据算法类型而定
  },
  "metadata": {
    // 处理元数据和诊断信息
  }
}
```

#### 检测算法响应格式

对于目标检测、人脸检测、车辆检测等算法，使用以下标准格式：

```json
{
  "success": true,
  "error": null,
  "data": {
    // --- 核心检测结果 ---
    "detections": [
      {
        // --- 必选字段 (所有检测算法都必须提供) ---
        "bbox": [132, 98, 308, 336],   // 统一为 [x_min, y_min, x_max, y_max]
        "confidence": 0.8829,          // 检测结果的置信度 (0.0-1.0)
        "label": "face",               // 统一使用 "label" 作为类别名称
        "class_id": 0,                 // (可选但推荐) 类别的数字ID

        // --- 可选的通用几何信息 ---
        // (前端可以根据bbox计算，但由算法提供更方便)
        "center": [220, 217],          // [x, y] 中心点坐标
        "width": 176,                  // 边界框宽度
        "height": 237,                 // 边界框高度

        // --- 扩展属性 (用于容纳所有算法的"个性"数据) ---
        "attributes": {
          "landmarks": [[x1, y1], [x2, y2], ...],  // 关键点坐标
          "feature_vector": [0.1, 0.2, ...],       // 特征向量
          "track_id": 15,                          // 目标跟踪ID
          "pose": {"yaw": 10.5, "pitch": -5.2},   // 姿态信息
          "quality_score": 0.95,                   // 质量评分
          "occlusion_ratio": 0.1                   // 遮挡比例
          // 任何其他算法特有的数据都放在这里
        }
      }
      // ... 其他检测对象
    ],

    // --- 结果元数据 ---
    "summary": {
      "num_detections": 1,             // 检测到的对象总数
      "class_counts": {                // 各类别的数量统计
        "face": 1,
        "person": 2
      },
      "confidence_stats": {            // 置信度统计
        "min": 0.65,
        "max": 0.95,
        "avg": 0.82
      }
    }
  },

  // --- 性能与诊断信息 ---
  "metadata": {
    "processing_time_ms": 802.68,               // 处理耗时(毫秒)
    "image_shape": [523, 440, 3],               // [height, width, channels]
    "timestamp_utc": "2025-07-30T12:55:00.123Z", // UTC时间戳
    "model_info": {
      "name": "yolov8n",
      "version": "1.0.0"
    },
    "hardware_info": {
      "device": "cuda:0",
      "memory_used_mb": 1024
    }
  }
}
```

#### 分类算法响应格式

对于图像分类、文本分类等算法：

```json
{
  "success": true,
  "error": null,
  "data": {
    "predictions": [
      {
        "label": "cat",               // 预测类别
        "class_id": 1,               // 类别ID
        "confidence": 0.95,          // 置信度
        "attributes": {
          "breed": "persian",        // 品种等扩展信息
          "age_estimate": "adult"
        }
      },
      {
        "label": "dog",
        "class_id": 2,
        "confidence": 0.03
      }
    ],
    "summary": {
      "top_prediction": "cat",
      "num_classes": 1000,
      "prediction_count": 5
    }
  },
  "metadata": {
    "processing_time_ms": 45.2,
    "image_shape": [224, 224, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

#### 错误响应格式

当处理失败时，使用以下格式：

```json
{
  "success": false,
  "error": {
    "code": "INVALID_FILE_FORMAT",           // 错误代码
    "message": "不支持的文件格式",            // 用户友好的错误消息
    "details": "支持的格式: jpg, png, mp4",  // 详细错误信息
    "timestamp": "2025-07-30T12:55:00.123Z"  // 错误发生时间
  },
  "data": null,
  "metadata": {
    "processing_time_ms": 12.5,
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

#### 字段规范说明

**必选字段**:
- `success`: 布尔值，表示处理是否成功
- `error`: 错误信息对象，成功时为null
- `data`: 核心业务数据，失败时为null
- `metadata`: 元数据信息

**检测结果字段**:
- `bbox`: 边界框坐标，统一使用 `[x_min, y_min, x_max, y_max]` 格式
- `confidence`: 置信度，范围 0.0-1.0
- `label`: 类别名称，统一使用此字段名
- `class_id`: 类别数字ID（推荐提供）
- `attributes`: 算法特有的扩展数据

**元数据字段**:
- `processing_time_ms`: 处理耗时（毫秒）
- `image_shape`: 图像尺寸 `[height, width, channels]`
- `timestamp_utc`: UTC时间戳

### 版本兼容性

- **当前版本**: v2.0（本规范）
- **向后兼容**: 支持v1.0格式，但推荐升级到v2.0
- **迁移指南**: 参见文档末尾的迁移指南

## 🔧 核心API接口

### 1. 管理与自省API

#### 1.1 健康检查
```http
GET /api/v1/health
```

**用途**: 容器健康检查，供Docker/K8s进行存活探针

**响应**:
```json
{
  "status": "ok",
  "timestamp": "2025-07-29T10:30:00.123Z"
}
```

#### 1.2 算法信息
```http
GET /api/v1/info
```

**用途**: 获取算法元信息，供平台展示算法详情

**响应**:
```json
{
  "algorithm_name": "人车非检测算法",
  "algorithm_version": "1.0.0",
  "algorithm_type": "目标检测",
  "description": "基于YOLOv8的人车非目标检测算法，支持实时检测和批量处理",
  "capabilities": {
    "input_modes": ["file", "stream", "batch"],
    "supported_formats": ["jpg", "jpeg", "png", "mp4", "avi", "rtsp"],
    "max_file_size_mb": 100,
    "concurrent_requests": 5
  },
  "model_info": {
    "framework": "PyTorch",
    "model_file": "yolov8n.pt",
    "input_size": [640, 640],
    "classes": ["person", "car", "bicycle", "motorcycle"]
  },
  "performance": {
    "avg_inference_time_ms": 50,
    "throughput_fps": 20,
    "memory_usage_mb": 512
  }
}
```

#### 1.3 运行状态
```http
GET /api/v1/status
```

**用途**: 获取算法当前运行状态

**响应**:
```json
{
  "status": "ready",
  "message": "模型已加载，准备接受任务",
  "uptime_seconds": 3600,
  "current_load": {
    "active_tasks": 2,
    "queue_length": 0,
    "cpu_percent": 15.5,
    "memory_used_mb": 1024,
    "gpu_memory_used_mb": 2048,
    "gpu_memory_total_mb": 8192
  },
  "statistics": {
    "total_requests": 1250,
    "successful_requests": 1200,
    "failed_requests": 50,
    "avg_response_time_ms": 45
  }
}
```

**状态值说明**:
- `ready`: 就绪，可接受请求
- `busy`: 繁忙，但仍可接受请求
- `loading`: 模型加载中
- `error`: 错误状态
- `maintenance`: 维护模式

### 2. 文件处理API

#### 2.1 同步处理（快速响应）
```http
POST /api/v1/detect
```

**用途**: 同步处理单个文件，适用于快速检测场景

**请求**: `multipart/form-data`
- `file`: 图片或视频文件
- `parameters`: JSON字符串（可选）

**参数示例**:
```json
{
  "confidence_threshold": 0.5,
  "iou_threshold": 0.4,
  "max_detections": 100,
  "return_crops": false,
  "annotate_image": true
}
```

**成功响应** (200 OK):
```json
{
  "success": true,
  "error": null,
  "data": {
    "detections": [
      {
        "bbox": [100, 150, 250, 300],
        "confidence": 0.92,
        "label": "person",
        "class_id": 0,
        "center": [175, 225],
        "width": 150,
        "height": 150,
        "attributes": {
          "pose": {"standing": true},
          "clothing": {"color": "blue"}
        }
      }
    ],
    "summary": {
      "num_detections": 1,
      "class_counts": {
        "person": 1
      },
      "confidence_stats": {
        "min": 0.92,
        "max": 0.92,
        "avg": 0.92
      }
    },
    "output_files": {
      "annotated_image": "/api/v1/static/results/annotated_12345.jpg"
    }
  },
  "metadata": {
    "processing_time_ms": 45,
    "image_shape": [1080, 1920, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z",
    "model_info": {
      "name": "yolov8n",
      "version": "1.0.0"
    }
  }
}
```

#### 2.2 异步处理（长时间任务）
```http
POST /api/v1/process/async
```

**用途**: 异步处理大文件或批量任务

**请求**: 同上

**成功响应** (202 Accepted):
```json
{
  "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status": "pending",
  "message": "任务已接收，正在排队处理",
  "estimated_time_seconds": 120
}
```

#### 2.3 查询异步任务结果
```http
GET /api/v1/results/{job_id}
```

**处理中响应**:
```json
{
  "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "status": "processing",
  "progress_percent": 45,
  "message": "正在处理第 45/100 帧",
  "estimated_remaining_seconds": 60
}
```

**完成响应**:
```json
{
  "success": true,
  "error": null,
  "data": {
    "job_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "status": "completed",
    "detections": [
      {
        "bbox": [120, 80, 340, 280],
        "confidence": 0.88,
        "label": "car",
        "class_id": 1,
        "center": [230, 180],
        "width": 220,
        "height": 200,
        "attributes": {
          "track_id": 15,
          "speed_kmh": 45.2
        }
      }
    ],
    "summary": {
      "num_detections": 1,
      "class_counts": {
        "car": 1
      }
    },
    "output_files": {
      "annotated_video": "/api/v1/static/results/output_12345.mp4",
      "detection_log": "/api/v1/static/results/detections_12345.json"
    }
  },
  "metadata": {
    "processing_time_ms": 95000,
    "image_shape": [720, 1280, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

### 3. 批量处理API

#### 3.1 批量文件处理
```http
POST /api/v1/process/batch
```

**用途**: 批量处理多个文件

**请求**: `multipart/form-data`
- `files`: 多个文件
- `parameters`: 统一参数

**响应**:
```json
{
  "batch_id": "batch_12345",
  "total_files": 10,
  "jobs": [
    {
      "file_name": "image1.jpg",
      "job_id": "job_001",
      "status": "pending"
    }
  ]
}
```

### 4. 实时流处理API

#### 4.1 WebSocket流处理
```
WS /api/v1/stream
```

**连接建立后发送启动指令**:
```json
{
  "action": "start",
  "stream_config": {
    "source_type": "rtsp",
    "source_url": "rtsp://user:pass@*************/stream",
    "fps": 25,
    "resolution": "1920x1080"
  },
  "parameters": {
    "confidence_threshold": 0.6,
    "send_interval_frames": 5,
    "roi_areas": [
      {
        "name": "entrance",
        "polygon": [[100, 100], [500, 100], [500, 400], [100, 400]]
      }
    ]
  }
}
```

**实时检测结果推送**:
```json
{
  "success": true,
  "error": null,
  "data": {
    "type": "detection",
    "frame_id": 1250,
    "detections": [
      {
        "bbox": [150, 200, 350, 450],
        "confidence": 0.88,
        "label": "person",
        "class_id": 0,
        "center": [250, 325],
        "width": 200,
        "height": 250,
        "attributes": {
          "track_id": 15,
          "roi_area": "entrance",
          "velocity": [2.5, -1.2]
        }
      }
    ],
    "summary": {
      "num_detections": 1,
      "class_counts": {
        "person": 1
      }
    },
    "stream_info": {
      "fps": 24.5,
      "frame_count": 1250,
      "dropped_frames": 2
    }
  },
  "metadata": {
    "processing_time_ms": 35.2,
    "image_shape": [720, 1280, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

### 5. 静态文件服务

#### 5.1 结果文件下载
```http
GET /api/v1/static/results/{filename}
```

**用途**: 下载处理结果文件（标注图片、视频等）

### 6. 模型管理API

#### 6.1 模型列表
```http
GET /api/v1/models
```

**响应**:
```json
{
  "models": [
    {
      "model_id": "yolov8n",
      "name": "YOLOv8 Nano",
      "version": "1.0.0",
      "status": "loaded",
      "accuracy": 0.85,
      "speed_ms": 20
    }
  ],
  "active_model": "yolov8n"
}
```

#### 6.2 切换模型
```http
POST /api/v1/models/{model_id}/activate
```

## 🚨 错误处理

### 统一错误响应格式

所有错误响应都必须遵循统一的响应格式规范：

```json
{
  "success": false,
  "error": {
    "code": "INVALID_FILE_FORMAT",
    "message": "不支持的文件格式",
    "details": "支持的格式: jpg, png, mp4",
    "timestamp": "2025-07-30T12:55:00.123Z"
  },
  "data": null,
  "metadata": {
    "processing_time_ms": 12.5,
    "timestamp_utc": "2025-07-30T12:55:00.123Z"
  }
}
```

### 常见错误码
- `INVALID_FILE_FORMAT`: 不支持的文件格式
- `FILE_TOO_LARGE`: 文件过大
- `MODEL_NOT_LOADED`: 模型未加载
- `INSUFFICIENT_RESOURCES`: 资源不足
- `PROCESSING_TIMEOUT`: 处理超时
- `INVALID_PARAMETERS`: 参数错误

## 📊 性能要求

### 响应时间要求
- 健康检查: < 100ms
- 同步检测: < 5s
- 状态查询: < 200ms
- 异步任务提交: < 500ms

### 并发要求
- 最少支持5个并发请求
- 队列长度至少50个任务
- 优雅处理资源不足情况

## 🔍 监控与日志

### 必需的监控指标
- 请求总数、成功率、失败率
- 平均响应时间、P95响应时间
- 当前活跃连接数
- 资源使用情况（CPU、内存、GPU）
- 模型推理时间

### 日志格式
```json
{
  "timestamp": "2025-07-29T10:30:00.123Z",
  "level": "INFO",
  "component": "detector",
  "message": "Processing completed",
  "job_id": "12345",
  "processing_time_ms": 45,
  "detections_count": 3
}
```

## 📝 实现示例

参考实现请查看：
- `algorithms/renchefei/` - 人车非检测算法实现示例
- `algorithms/template/` - 算法模板项目

## 🔄 版本管理

- API版本通过URL路径管理（/api/v1, /api/v2）
- 向后兼容性要求
- 废弃API的迁移指南

## 📁 推荐项目结构

使用uv管理的算法项目应遵循以下结构：

```
your-algorithm/
├── pyproject.toml          # 项目配置和依赖管理
├── uv.lock                 # 锁定的依赖版本
├── Dockerfile              # 容器构建文件
├── README.md               # 项目说明
├── src/                    # 源代码目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config.py          # 配置管理
│   ├── api/               # API路由
│   │   ├── __init__.py
│   │   ├── health.py      # 健康检查
│   │   ├── info.py        # 算法信息
│   │   └── detection.py   # 检测接口
│   ├── core/              # 核心算法
│   │   ├── __init__.py
│   │   └── algorithm.py   # 算法实现
│   └── models/            # 数据模型
│       ├── __init__.py
│       └── detection.py   # 请求/响应模型
├── models/                # 模型文件目录
│   └── your_model.pt
├── config/                # 配置文件目录
│   └── settings.yaml
├── tests/                 # 测试文件
│   ├── __init__.py
│   ├── test_api.py
│   └── test_algorithm.py
└── static/                # 静态文件
    └── results/           # 结果输出目录
```

### pyproject.toml 配置示例

```toml
[project]
name = "your-algorithm"
version = "1.0.0"
description = "算法描述"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    # 其他依赖...
]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "black>=23.0.0",
    "mypy>=1.5.0",
]
```

## 📚 参考资源

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [uv包管理器文档](https://docs.astral.sh/uv/)
- [OpenAPI规范](https://swagger.io/specification/)
- [Docker最佳实践](https://docs.docker.com/develop/dev-best-practices/)
- [算法集成指南](./INTEGRATION_GUIDE.md)
- [算法模板项目](./template/)

## 📈 迁移指南

### 从v1.0到v2.0的迁移

#### 主要变更

1. **响应结构调整**:
   ```json
   // v1.0 格式
   {
     "success": true,
     "processing_time_ms": 45,
     "results": {...}
   }

   // v2.0 格式
   {
     "success": true,
     "error": null,
     "data": {...},
     "metadata": {
       "processing_time_ms": 45,
       "timestamp_utc": "2025-07-30T12:55:00.123Z"
     }
   }
   ```

2. **边界框格式变更**:
   ```json
   // v1.0 格式
   "bbox": {
     "x_min": 100, "y_min": 150,
     "x_max": 250, "y_max": 300,
     "width": 150, "height": 150
   }

   // v2.0 格式
   "bbox": [100, 150, 250, 300],
   "width": 150,
   "height": 150
   ```

3. **字段名称统一**:
   - `class_name` → `label`
   - `total_detections` → `num_detections`
   - `classes_detected` → `class_counts`

4. **新增扩展字段**:
   ```json
   "attributes": {
     "track_id": 15,
     "landmarks": [[x1, y1], [x2, y2]],
     "feature_vector": [0.1, 0.2, 0.3]
   }
   ```

#### 迁移步骤

1. **更新数据模型**: 修改Pydantic模型以符合新格式
2. **调整API响应**: 确保所有端点返回新格式
3. **更新客户端代码**: 修改前端和调用方代码以处理新格式
4. **测试验证**: 全面测试确保兼容性

#### 兼容性支持

- 平台将在6个月内同时支持v1.0和v2.0格式
- 新开发的算法必须使用v2.0格式
- 现有算法建议在下次更新时迁移到v2.0

## 📈 开发计划与项目状态

### 当前项目状态 (2025-07-30)

#### ✅ 已完成的工作

**1. 统一响应格式v2.0规范制定**
- 完整的API规范文档编写
- JSON Schema定义和验证
- 响应格式标准化
- 错误处理统一化
- 迁移指南制定

**2. 模板算法包 (algorithms/template/)**
- 完全重写数据模型以符合v2.0规范
- 统一响应格式实现
- 工具函数和转换器
- 完整的测试脚本
- 详细的开发文档

**3. 现有算法包升级**

| 算法包 | 状态 | 版本 | 部署端口 | 功能 |
|--------|------|------|----------|------|
| **renchefei** | ✅ 完成 | v2.0 | 8002 | 人车非检测 |
| **wenzhou_face** | ✅ 完成 | v2.0 | 8003 | 人脸识别 |
| **accident_classify** | ⏳ 待更新 | v1.0 | - | 事故分类 |

**4. Docker容器化部署**
- 所有v2.0算法已容器化
- 健康检查机制完善
- 统一的构建和部署流程
- 容器标签标准化

#### 🔧 技术架构

**核心技术栈**
- **Web框架**: FastAPI + Uvicorn
- **包管理**: uv (Python包管理器)
- **容器化**: Docker + Docker Compose
- **数据验证**: Pydantic v2
- **API文档**: OpenAPI 3.0 (自动生成)

**统一响应格式v2.0特性**
```json
{
  "success": boolean,           // 执行状态
  "error": ErrorInfo | null,    // 错误信息
  "data": BusinessData | null,  // 业务数据
  "metadata": {                 // 元数据
    "processing_time_ms": float,
    "image_shape": [int, int, int],
    "timestamp_utc": string,
    "model_info": {...},
    "hardware_info": {...}
  }
}
```

**关键改进**
- 边界框格式: `[x_min, y_min, x_max, y_max]` (数组格式)
- 统一字段命名: `label` (替代 `class_name`)
- 扩展属性支持: `attributes` 字段
- 丰富的元数据信息
- 结构化错误处理

#### 🚀 部署环境

**当前运行的容器**
```bash
# 生产环境容器
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Ports}}"

NAMES               IMAGE                 PORTS
renchefei-v2        renchefei:v2.0       0.0.0.0:8002->8000/tcp
wenzhou-face-v2     wenzhou_face:v2.0    0.0.0.0:8003->8001/tcp
```

**API访问地址**
- renchefei算法: http://localhost:8002
- wenzhou_face算法: http://localhost:8003
- API文档: http://localhost:{port}/docs

### 📋 后续开发计划

#### Phase 1: 算法包完善 (预计1周)

**1.1 完成剩余算法升级**
- [ ] accident_classify算法升级到v2.0
- [ ] 统一所有算法的响应格式
- [ ] 完善错误处理机制

**1.2 质量保证**
- [ ] 单元测试覆盖率达到80%+
- [ ] 集成测试自动化
- [ ] 性能基准测试
- [ ] 内存泄漏检测

#### Phase 2: 平台集成 (预计2周)

**2.1 算法管理平台集成**
- [ ] 与algorithm-platform-manager前后端集成
- [ ] 算法注册和发现机制
- [ ] 动态负载均衡
- [ ] 容器生命周期管理

**2.2 前端验证**
- [ ] 使用Playwright MCP进行自动化测试
- [ ] 图片上传和结果渲染验证
- [ ] 检测框绘制功能测试
- [ ] 响应式设计适配

#### Phase 3: 生产就绪 (预计2周)

**3.1 监控和运维**
- [ ] Prometheus指标收集
- [ ] Grafana监控面板
- [ ] 日志聚合和分析
- [ ] 告警机制建立

**3.2 安全和性能**
- [ ] API认证和授权
- [ ] 请求限流和防护
- [ ] 缓存策略优化
- [ ] 数据库连接池

**3.3 文档和培训**
- [ ] 完整的部署文档
- [ ] 开发者指南
- [ ] 故障排查手册
- [ ] 团队培训材料

### 🔄 版本迭代计划

#### v2.1.0 (计划: 2025-08-15)
- 批量处理API优化
- 异步任务队列支持
- 结果缓存机制
- 性能监控增强

#### v2.2.0 (计划: 2025-09-01)
- 多模型支持
- A/B测试框架
- 模型版本管理
- 热更新机制

#### v3.0.0 (计划: 2025-10-01)
- 微服务架构重构
- 服务网格集成
- 云原生部署
- 多云支持

### 📊 关键指标

**当前性能指标**
- 平均响应时间: < 100ms (健康检查)
- 容器启动时间: < 30s
- 内存使用: < 512MB (基础容器)
- CPU使用: < 50% (空闲状态)

**目标指标 (v2.1.0)**
- 平均响应时间: < 200ms (检测任务)
- 并发处理能力: 10+ 请求/秒
- 可用性: 99.9%
- 错误率: < 0.1%

### 🛠️ 开发工具和流程

**开发环境**
- Python 3.11+
- uv包管理器
- Docker Desktop
- VS Code + Python扩展

**CI/CD流程**
- 代码提交触发自动测试
- Docker镜像自动构建
- 容器健康检查验证
- 自动部署到测试环境

**代码质量**
- Black代码格式化
- Flake8代码检查
- mypy类型检查
- pytest单元测试

### 📞 联系信息

**技术负责人**: 算法平台团队
**文档维护**: 持续更新
**问题反馈**: GitHub Issues
**紧急联系**: 团队内部沟通渠道

---

**版本**: v2.0.0
**更新时间**: 2025-07-30
**维护者**: 算法平台团队
