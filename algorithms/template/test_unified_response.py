#!/usr/bin/env python3
"""
测试统一响应格式 v2.0
"""

import json
from datetime import datetime
from src.models.detection import (
    Detection,
    DetectionData,
    DetectionSummary,
    ConfidenceStats,
    Metadata,
    ModelInfo,
    HardwareInfo,
    create_success_response,
    create_error_response
)


def test_detection_model():
    """测试检测结果模型"""
    print("🧪 测试检测结果模型...")
    
    # 创建检测结果
    detection = Detection(
        bbox=[100, 150, 250, 300],
        confidence=0.92,
        label="person",
        class_id=0,
        attributes={
            "pose": {"standing": True},
            "clothing": {"color": "blue"},
            "track_id": 15
        }
    )
    
    print(f"✅ 检测结果: {detection.model_dump()}")
    
    # 验证自动计算的几何信息
    assert detection.center == [175.0, 225.0]
    assert detection.width == 150.0
    assert detection.height == 150.0
    print("✅ 几何信息自动计算正确")


def test_success_response():
    """测试成功响应格式"""
    print("\n🧪 测试成功响应格式...")
    
    # 创建检测数据
    detections = [
        Detection(
            bbox=[100, 150, 250, 300],
            confidence=0.92,
            label="person",
            class_id=0,
            attributes={"track_id": 15}
        ),
        Detection(
            bbox=[300, 200, 450, 350],
            confidence=0.88,
            label="car",
            class_id=1,
            attributes={"speed_kmh": 45.2}
        )
    ]
    
    summary = DetectionSummary(
        num_detections=2,
        class_counts={"person": 1, "car": 1},
        confidence_stats=ConfidenceStats(
            min=0.88,
            max=0.92,
            avg=0.90
        )
    )
    
    detection_data = DetectionData(
        detections=detections,
        summary=summary,
        output_files={
            "annotated_image": "/api/v1/static/results/annotated_12345.jpg"
        }
    )
    
    metadata = Metadata(
        processing_time_ms=45.2,
        image_shape=[1080, 1920, 3],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="test_algorithm",
            version="1.0.0"
        ),
        hardware_info=HardwareInfo(
            device="cpu",
            memory_used_mb=512.0
        )
    )
    
    # 创建成功响应
    response = create_success_response(
        data=detection_data,
        metadata=metadata
    )
    
    # 验证响应结构
    response_dict = response.model_dump()
    assert response_dict["success"] is True
    assert response_dict["error"] is None
    assert "data" in response_dict
    assert "metadata" in response_dict
    
    print("✅ 成功响应格式正确")
    print(f"📄 响应示例:\n{json.dumps(response_dict, indent=2, ensure_ascii=False)}")


def test_error_response():
    """测试错误响应格式"""
    print("\n🧪 测试错误响应格式...")
    
    # 创建错误响应
    response = create_error_response(
        error_code="INVALID_FILE_FORMAT",
        error_message="不支持的文件格式",
        error_details="仅支持图片和视频文件"
    )
    
    # 验证响应结构
    response_dict = response.model_dump()
    assert response_dict["success"] is False
    assert response_dict["error"] is not None
    assert response_dict["data"] is None
    assert "metadata" in response_dict
    
    print("✅ 错误响应格式正确")
    print(f"📄 错误响应示例:\n{json.dumps(response_dict, indent=2, ensure_ascii=False)}")


def test_bbox_format():
    """测试边界框格式"""
    print("\n🧪 测试边界框格式...")
    
    detection = Detection(
        bbox=[132, 98, 308, 336],
        confidence=0.8829,
        label="face",
        class_id=0
    )
    
    # 验证bbox格式
    assert isinstance(detection.bbox, list)
    assert len(detection.bbox) == 4
    assert detection.bbox == [132, 98, 308, 336]
    
    # 验证自动计算的几何信息
    expected_center = [(132 + 308) / 2, (98 + 336) / 2]
    expected_width = 308 - 132
    expected_height = 336 - 98
    
    assert detection.center == expected_center
    assert detection.width == expected_width
    assert detection.height == expected_height
    
    print("✅ 边界框格式和几何计算正确")


def test_attributes_field():
    """测试扩展属性字段"""
    print("\n🧪 测试扩展属性字段...")
    
    detection = Detection(
        bbox=[100, 100, 200, 200],
        confidence=0.95,
        label="face",
        class_id=0,
        attributes={
            "landmarks": [[110, 120], [130, 120], [120, 140], [115, 160], [125, 160]],
            "feature_vector": [0.1, 0.2, 0.3, 0.4, 0.5],
            "track_id": 15,
            "pose": {"yaw": 10.5, "pitch": -5.2, "roll": 2.1},
            "quality_score": 0.95,
            "occlusion_ratio": 0.1,
            "age_estimate": 25,
            "gender": "male",
            "emotion": "happy"
        }
    )
    
    # 验证扩展属性
    assert "landmarks" in detection.attributes
    assert "feature_vector" in detection.attributes
    assert "track_id" in detection.attributes
    assert detection.attributes["track_id"] == 15
    
    print("✅ 扩展属性字段支持各种算法特有数据")
    print(f"📄 扩展属性示例: {json.dumps(detection.attributes, indent=2, ensure_ascii=False)}")


if __name__ == "__main__":
    print("🚀 开始测试统一响应格式 v2.0\n")
    
    try:
        test_detection_model()
        test_success_response()
        test_error_response()
        test_bbox_format()
        test_attributes_field()
        
        print("\n🎉 所有测试通过！统一响应格式 v2.0 工作正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise
