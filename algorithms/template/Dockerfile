# 算法容器模板 - 基于统一API规范
FROM python:3.11-slim

# 设置算法标签（必需）
LABEL algorithm.platform="true"
LABEL algorithm.name="算法模板"
LABEL algorithm.type="目标检测"
LABEL algorithm.version="1.0.0"
LABEL algorithm.description="算法容器开发模板，遵循统一API规范"

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install --no-cache-dir uv

# 复制项目配置文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN uv sync --frozen --no-dev

# 复制源代码
COPY src/ ./src/
COPY models/ ./models/
COPY config/ ./config/

# 创建结果目录
RUN mkdir -p /app/static/results

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# 启动命令
CMD ["uv", "run", "python", "-m", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
