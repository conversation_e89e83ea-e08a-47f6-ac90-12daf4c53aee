#!/bin/bash

# 算法容器构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
ALGORITHM_NAME="algorithm-template"
VERSION="1.0.0"
REGISTRY=""  # 如果需要推送到私有仓库，设置这个值

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 构建镜像
build_image() {
    print_info "开始构建算法容器镜像..."
    
    local image_tag="${ALGORITHM_NAME}:${VERSION}"
    if [ -n "$REGISTRY" ]; then
        image_tag="${REGISTRY}/${image_tag}"
    fi
    
    print_info "镜像标签: ${image_tag}"
    
    # 构建镜像
    docker build \
        --tag "${image_tag}" \
        --tag "${ALGORITHM_NAME}:latest" \
        --build-arg VERSION="${VERSION}" \
        .
    
    if [ $? -eq 0 ]; then
        print_success "镜像构建成功: ${image_tag}"
    else
        print_error "镜像构建失败"
        exit 1
    fi
}

# 测试镜像
test_image() {
    print_info "测试算法容器..."
    
    local container_name="${ALGORITHM_NAME}-test"
    local image_tag="${ALGORITHM_NAME}:latest"
    
    # 清理可能存在的测试容器
    docker rm -f "${container_name}" 2>/dev/null || true
    
    # 启动测试容器
    print_info "启动测试容器..."
    docker run -d \
        --name "${container_name}" \
        -p 8000:8000 \
        "${image_tag}"
    
    # 等待容器启动
    print_info "等待容器启动..."
    sleep 10
    
    # 测试健康检查
    print_info "测试健康检查接口..."
    if curl -f http://localhost:8000/api/v1/health > /dev/null 2>&1; then
        print_success "健康检查通过"
    else
        print_error "健康检查失败"
        docker logs "${container_name}"
        docker rm -f "${container_name}"
        exit 1
    fi
    
    # 测试算法信息接口
    print_info "测试算法信息接口..."
    if curl -f http://localhost:8000/api/v1/info > /dev/null 2>&1; then
        print_success "算法信息接口测试通过"
    else
        print_error "算法信息接口测试失败"
        docker logs "${container_name}"
        docker rm -f "${container_name}"
        exit 1
    fi
    
    # 清理测试容器
    docker rm -f "${container_name}"
    print_success "容器测试完成"
}

# 推送镜像
push_image() {
    if [ -z "$REGISTRY" ]; then
        print_warning "未配置镜像仓库，跳过推送"
        return
    fi
    
    print_info "推送镜像到仓库..."
    
    local image_tag="${REGISTRY}/${ALGORITHM_NAME}:${VERSION}"
    
    docker push "${image_tag}"
    
    if [ $? -eq 0 ]; then
        print_success "镜像推送成功: ${image_tag}"
    else
        print_error "镜像推送失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "算法容器构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     构建镜像"
    echo "  test      测试镜像"
    echo "  push      推送镜像"
    echo "  all       构建、测试并推送镜像"
    echo "  clean     清理构建缓存"
    echo "  help      显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  ALGORITHM_NAME  算法名称 (默认: algorithm-template)"
    echo "  VERSION         版本号 (默认: 1.0.0)"
    echo "  REGISTRY        镜像仓库地址"
    echo ""
    echo "示例:"
    echo "  $0 build                    # 构建镜像"
    echo "  $0 all                      # 构建、测试并推送"
    echo "  REGISTRY=my-registry.com $0 push  # 推送到指定仓库"
}

# 清理构建缓存
clean_cache() {
    print_info "清理Docker构建缓存..."
    docker builder prune -f
    print_success "构建缓存清理完成"
}

# 主函数
main() {
    case "${1:-build}" in
        "build")
            check_docker
            build_image
            ;;
        "test")
            check_docker
            test_image
            ;;
        "push")
            check_docker
            push_image
            ;;
        "all")
            check_docker
            build_image
            test_image
            push_image
            ;;
        "clean")
            check_docker
            clean_cache
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
