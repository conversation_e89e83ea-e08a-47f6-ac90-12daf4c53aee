[project]
name = "algorithm-template"
version = "1.0.0"
description = "算法容器开发模板，遵循统一API规范"
authors = [
    {name = "Algorithm Team", email = "<EMAIL>"}
]
requires-python = ">=3.11"
dependencies = [
    # Web框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    
    # 文件处理
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.1",
    
    # 图像处理
    "opencv-python>=4.8.0",
    "Pillow>=10.0.0",
    "numpy>=1.24.0",
    
    # 深度学习框架（根据需要选择）
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    # "tensorflow>=2.13.0",
    
    # 数据处理
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # 异步任务
    "celery>=5.3.0",
    "redis>=5.0.0",
    
    # 工具库
    "loguru>=0.7.2",
    "python-jose>=3.3.0",
    "passlib>=1.7.4",
    
    # 监控
    "psutil>=5.9.0",
    "prometheus-client>=0.19.0",
    
    # WebSocket支持
    "websockets>=12.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

gpu = [
    # GPU 加速支持
    "torch[cuda]>=2.0.0; sys_platform == 'linux'",
]

[project.urls]
Homepage = "https://github.com/algorithm-platform/template"
Repository = "https://github.com/algorithm-platform/template.git"
Documentation = "https://algorithm-platform.readthedocs.io"
"Bug Tracker" = "https://github.com/algorithm-platform/template/issues"

[project.scripts]
algorithm-template = "src.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
]
