# 算法容器模板 (v2.0)

这是一个遵循统一API规范v2.0的算法容器开发模板，基于FastAPI框架构建，采用统一响应格式。

## 🚀 快速开始

### 1. 复制模板
```bash
cp -r algorithms/template algorithms/your-algorithm-name
cd algorithms/your-algorithm-name
```

### 2. 修改配置
编辑以下文件以适配你的算法：

**Dockerfile**:
```dockerfile
LABEL algorithm.name="你的算法名称"
LABEL algorithm.type="算法类型"
LABEL algorithm.description="算法描述"
```

**src/config.py**:
```python
algorithm_name: str = "你的算法名称"
algorithm_type: str = "算法类型"
algorithm_description: str = "算法描述"
model_path: str = "models/your_model.pt"
```

### 3. 安装依赖
```bash
# 安装uv包管理器（如果未安装）
pip install uv

# 安装项目依赖
uv sync

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows
```

### 4. 实现算法逻辑
主要需要修改的文件：

- `src/core/algorithm.py` - 核心算法实现
- `src/models/detection.py` - 数据模型（如果需要）
- `pyproject.toml` - 添加你的依赖

### 5. 构建和测试
```bash
# 构建镜像
docker build -t your-algorithm:latest .

# 运行容器
docker run -d -p 8000:8000 --name your-algorithm your-algorithm:latest

# 测试API
curl http://localhost:8000/api/v1/health
curl http://localhost:8000/api/v1/info
```

## 📋 API接口

### 管理接口
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/info` - 算法信息
- `GET /api/v1/status` - 运行状态

### 处理接口
- `POST /api/v1/detect` - 同步检测
- `POST /api/v1/predict` - 通用预测
- `POST /api/v1/process/async` - 异步处理
- `GET /api/v1/results/{job_id}` - 查询异步结果

### 流处理接口
- `WS /api/v1/stream` - WebSocket流处理

### 静态文件
- `GET /api/v1/static/results/{filename}` - 下载结果文件

## 🔧 自定义实现

### 1. 算法引擎 (src/core/algorithm.py)

```python
async def _load_model(self):
    """加载你的模型"""
    import torch
    self.model = torch.load(self.model_path, map_location=self.device)
    self.model.eval()

async def _run_inference(self, image: np.ndarray, parameters: DetectionRequest):
    """实现你的推理逻辑"""
    # 预处理
    input_tensor = self.preprocess(image)
    
    # 推理
    with torch.no_grad():
        outputs = self.model(input_tensor)
    
    # 后处理
    detections = self.postprocess(outputs, parameters)
    
    return detections
```

### 2. 数据模型 (src/models/detection.py)

根据你的算法输出格式，修改 `Detection` 和相关模型：

```python
class Detection(BaseModel):
    """根据你的算法输出调整"""
    class_id: int
    class_name: str
    confidence: float
    bbox: BoundingBox
    # 添加你的特定字段
    custom_field: Optional[str] = None
```

### 3. 配置参数 (src/config.py)

添加你的算法特定配置：

```python
class Settings(BaseSettings):
    # 你的自定义配置
    custom_param1: float = 0.5
    custom_param2: int = 100
    custom_param3: str = "default_value"
```

## 📊 监控和日志

### 健康检查
容器自带健康检查，确保服务正常运行：
```bash
docker ps  # 查看健康状态
```

### 日志查看
```bash
docker logs your-algorithm
```

### 性能监控
访问 `/api/v1/status` 查看实时性能指标。

## 🧪 测试

### 单元测试
```bash
# 添加测试文件到 tests/ 目录
python -m pytest tests/
```

### API测试
```bash
# 使用提供的测试脚本
python test_api.py
```

### 压力测试
```bash
# 使用ab或其他工具进行压力测试
ab -n 1000 -c 10 http://localhost:8000/api/v1/health
```

## 📦 部署

### 本地部署
```bash
docker run -d -p 8000:8000 your-algorithm:latest
```

### 生产部署
```bash
# 使用docker-compose
version: '3.8'
services:
  your-algorithm:
    image: your-algorithm:latest
    ports:
      - "8000:8000"
    environment:
      - ALGORITHM_MODEL_DEVICE=cuda
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

## 🔍 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径
   - 确认模型格式兼容性
   - 查看内存使用情况

2. **推理速度慢**
   - 检查设备配置 (CPU/GPU)
   - 优化模型大小
   - 调整批处理大小

3. **内存不足**
   - 减少并发任务数
   - 优化图像预处理
   - 使用模型量化

### 调试模式
```bash
# 启用调试模式
docker run -e ALGORITHM_DEBUG=true your-algorithm:latest
```

## 📚 参考资料

- [统一API规范](../ALGORITHM_API_SPECIFICATION.md)
- [FastAPI文档](https://fastapi.tiangolo.com/)
- [Docker最佳实践](https://docs.docker.com/develop/dev-best-practices/)

## 🤝 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

MIT License
