#!/usr/bin/env python3
"""
算法容器主应用 - 遵循统一API规范
"""

import os
import asyncio
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

from .config import settings
from .api import health, info, detection, async_processing, stream
from .core.algorithm import AlgorithmEngine
from .core.task_manager import TaskManager


# 全局变量
algorithm_engine = None
task_manager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global algorithm_engine, task_manager
    
    # 启动时初始化
    print("🚀 启动算法服务...")
    
    # 初始化算法引擎
    algorithm_engine = AlgorithmEngine()
    await algorithm_engine.initialize()
    
    # 初始化任务管理器
    task_manager = TaskManager()
    await task_manager.initialize()
    
    print("✅ 算法服务启动完成")
    
    yield
    
    # 关闭时清理
    print("🛑 关闭算法服务...")
    
    if task_manager:
        await task_manager.cleanup()
    
    if algorithm_engine:
        await algorithm_engine.cleanup()
    
    print("✅ 算法服务关闭完成")


# 创建FastAPI应用
app = FastAPI(
    title=settings.algorithm_name,
    description=settings.algorithm_description,
    version=settings.algorithm_version,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(health.router, prefix="/api/v1", tags=["健康检查"])
app.include_router(info.router, prefix="/api/v1", tags=["算法信息"])
app.include_router(detection.router, prefix="/api/v1", tags=["检测API"])
app.include_router(async_processing.router, prefix="/api/v1", tags=["异步处理"])
app.include_router(stream.router, prefix="/api/v1", tags=["流处理"])

# 静态文件服务
app.mount("/api/v1/static", StaticFiles(directory="static"), name="static")

# 根路径
@app.get("/")
async def root():
    return {
        "algorithm_name": settings.algorithm_name,
        "version": settings.algorithm_version,
        "status": "running",
        "docs": "/api/docs",
        "health": "/api/v1/health"
    }


# 获取全局实例的辅助函数
def get_algorithm_engine() -> AlgorithmEngine:
    """获取算法引擎实例"""
    if algorithm_engine is None:
        raise HTTPException(status_code=503, detail="算法引擎未初始化")
    return algorithm_engine


def get_task_manager() -> TaskManager:
    """获取任务管理器实例"""
    if task_manager is None:
        raise HTTPException(status_code=503, detail="任务管理器未初始化")
    return task_manager


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
