"""
算法引擎核心类
"""

import asyncio
import io
import uuid
from pathlib import Path
from typing import Any, Dict, List, Optional

import cv2
import numpy as np
from PIL import Image

from ..config import settings
from ..models.detection import (
    DetectionRequest, DetectionResults, Detection, 
    BoundingBox, Point, DetectionSummary, ImageSize, OutputFiles
)


class AlgorithmEngine:
    """算法引擎"""
    
    def __init__(self):
        self.model = None
        self.is_initialized = False
        self.device = settings.model_device
        
    async def initialize(self):
        """初始化算法引擎"""
        try:
            print(f"🔧 初始化算法引擎...")
            print(f"📍 设备: {self.device}")
            print(f"📁 模型路径: {settings.model_path}")
            
            # 加载模型（这里是示例，需要根据实际算法实现）
            await self._load_model()
            
            self.is_initialized = True
            print("✅ 算法引擎初始化完成")
            
        except Exception as e:
            print(f"❌ 算法引擎初始化失败: {e}")
            raise
    
    async def _load_model(self):
        """加载模型"""
        # 这里是示例实现，需要根据实际算法替换
        # 例如：
        # import torch
        # self.model = torch.load(settings.model_path, map_location=self.device)
        # self.model.eval()
        
        # 模拟加载时间
        await asyncio.sleep(1)
        self.model = "dummy_model"  # 占位符
        
    async def detect(
        self, 
        file_content: bytes, 
        filename: str, 
        parameters: DetectionRequest
    ) -> DetectionResults:
        """执行检测"""
        if not self.is_initialized:
            raise RuntimeError("算法引擎未初始化")
        
        # 解析图像
        image = self._parse_image(file_content)
        
        # 执行推理
        detections = await self._run_inference(image, parameters)
        
        # 生成输出文件（如果需要）
        output_files = None
        if parameters.annotate_image:
            output_files = await self._generate_output_files(
                image, detections, filename
            )
        
        # 构建结果
        return DetectionResults(
            detections=detections,
            summary=DetectionSummary(
                total_detections=len(detections),
                classes_detected=list(set(d.class_name for d in detections)),
                image_size=ImageSize(width=image.shape[1], height=image.shape[0])
            )
        )
    
    def _parse_image(self, file_content: bytes) -> np.ndarray:
        """解析图像文件"""
        try:
            # 使用PIL解析图像
            image = Image.open(io.BytesIO(file_content))
            
            # 转换为RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 转换为numpy数组
            image_array = np.array(image)
            
            # 转换为OpenCV格式 (BGR)
            image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            return image_bgr
            
        except Exception as e:
            raise ValueError(f"图像解析失败: {e}")
    
    async def _run_inference(
        self, 
        image: np.ndarray, 
        parameters: DetectionRequest
    ) -> List[Detection]:
        """运行推理"""
        # 这里是示例实现，需要根据实际算法替换
        
        # 模拟推理时间
        await asyncio.sleep(0.05)
        
        # 生成示例检测结果
        height, width = image.shape[:2]
        
        # 模拟检测结果
        detections = []
        
        # 示例：在图像中心生成一个检测框
        if np.random.random() > 0.3:  # 70%概率检测到目标
            center_x = width // 2
            center_y = height // 2
            box_width = min(width, height) // 4
            box_height = box_width
            
            x_min = max(0, center_x - box_width // 2)
            y_min = max(0, center_y - box_height // 2)
            x_max = min(width, center_x + box_width // 2)
            y_max = min(height, center_y + box_height // 2)
            
            confidence = 0.5 + np.random.random() * 0.4  # 0.5-0.9
            
            if confidence >= parameters.confidence_threshold:
                detection = Detection(
                    class_id=0,
                    class_name="person",
                    confidence=confidence,
                    bbox=BoundingBox(
                        x_min=float(x_min),
                        y_min=float(y_min),
                        x_max=float(x_max),
                        y_max=float(y_max),
                        width=float(x_max - x_min),
                        height=float(y_max - y_min)
                    ),
                    center=Point(
                        x=float(center_x),
                        y=float(center_y)
                    )
                )
                detections.append(detection)
        
        return detections
    
    async def _generate_output_files(
        self, 
        image: np.ndarray, 
        detections: List[Detection], 
        filename: str
    ) -> OutputFiles:
        """生成输出文件"""
        # 创建标注图像
        annotated_image = image.copy()
        
        # 绘制检测框
        for detection in detections:
            bbox = detection.bbox
            
            # 绘制边界框
            cv2.rectangle(
                annotated_image,
                (int(bbox.x_min), int(bbox.y_min)),
                (int(bbox.x_max), int(bbox.y_max)),
                (0, 255, 0),  # 绿色
                2
            )
            
            # 绘制标签
            label = f"{detection.class_name}: {detection.confidence:.2f}"
            cv2.putText(
                annotated_image,
                label,
                (int(bbox.x_min), int(bbox.y_min) - 10),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (0, 255, 0),
                1
            )
        
        # 保存标注图像
        output_filename = f"annotated_{uuid.uuid4().hex[:8]}_{filename}"
        output_path = Path(settings.results_dir) / output_filename
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        cv2.imwrite(str(output_path), annotated_image)
        
        return OutputFiles(
            annotated_image=f"/api/v1/static/results/{output_filename}"
        )
    
    async def cleanup(self):
        """清理资源"""
        print("🧹 清理算法引擎资源...")
        self.model = None
        self.is_initialized = False
        print("✅ 算法引擎资源清理完成")
