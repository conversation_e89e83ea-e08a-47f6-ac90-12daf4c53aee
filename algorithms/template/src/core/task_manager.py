"""
异步任务管理器
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, Optional
from enum import Enum

from ..models.detection import TaskStatus


class TaskStatusEnum(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class Task:
    """任务对象"""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.status = TaskStatusEnum.PENDING
        self.created_at = datetime.utcnow()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.progress_percent = 0
        self.message = "任务已创建"
        self.result = None
        self.error_message = None


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.max_concurrent_tasks = 5
        self.running_tasks = 0
        
    async def initialize(self):
        """初始化任务管理器"""
        print("📋 初始化任务管理器...")
        print("✅ 任务管理器初始化完成")
    
    def create_task(self) -> str:
        """创建新任务"""
        job_id = str(uuid.uuid4())
        task = Task(job_id)
        self.tasks[job_id] = task
        return job_id
    
    def get_task_status(self, job_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        task = self.tasks.get(job_id)
        if not task:
            return None
        
        processing_time = None
        if task.started_at:
            end_time = task.completed_at or datetime.utcnow()
            processing_time = (end_time - task.started_at).total_seconds()
        
        return TaskStatus(
            job_id=job_id,
            status=task.status.value,
            progress_percent=task.progress_percent,
            message=task.message,
            processing_time_seconds=processing_time,
            results=task.result,
            error_message=task.error_message
        )
    
    async def execute_task(self, job_id: str, task_func, *args, **kwargs):
        """执行异步任务"""
        task = self.tasks.get(job_id)
        if not task:
            return
        
        try:
            # 检查并发限制
            if self.running_tasks >= self.max_concurrent_tasks:
                task.status = TaskStatusEnum.PENDING
                task.message = "等待资源可用..."
                
                # 等待资源可用
                while self.running_tasks >= self.max_concurrent_tasks:
                    await asyncio.sleep(1)
            
            # 开始执行
            self.running_tasks += 1
            task.status = TaskStatusEnum.PROCESSING
            task.started_at = datetime.utcnow()
            task.message = "正在处理..."
            
            # 执行任务
            result = await task_func(*args, **kwargs)
            
            # 任务完成
            task.status = TaskStatusEnum.COMPLETED
            task.completed_at = datetime.utcnow()
            task.progress_percent = 100
            task.message = "处理完成"
            task.result = result
            
        except Exception as e:
            # 任务失败
            task.status = TaskStatusEnum.FAILED
            task.completed_at = datetime.utcnow()
            task.message = "处理失败"
            task.error_message = str(e)
            
        finally:
            self.running_tasks -= 1
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        current_time = datetime.utcnow()
        expired_tasks = []
        
        for job_id, task in self.tasks.items():
            age_hours = (current_time - task.created_at).total_seconds() / 3600
            if age_hours > max_age_hours:
                expired_tasks.append(job_id)
        
        for job_id in expired_tasks:
            del self.tasks[job_id]
        
        if expired_tasks:
            print(f"🧹 清理了 {len(expired_tasks)} 个过期任务")
    
    async def cleanup(self):
        """清理资源"""
        print("🧹 清理任务管理器资源...")
        self.tasks.clear()
        print("✅ 任务管理器资源清理完成")
