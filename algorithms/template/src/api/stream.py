"""
流处理API - WebSocket
"""

import json
import asyncio
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from pydantic import BaseModel

from ..main import get_algorithm_engine

router = APIRouter()


class StreamConfig(BaseModel):
    """流配置"""
    source_type: str  # rtsp, file, camera
    source_url: str
    fps: int = 25
    resolution: str = "1920x1080"


class StreamCommand(BaseModel):
    """流命令"""
    action: str  # start, stop, pause, resume
    stream_config: StreamConfig = None
    parameters: Dict[str, Any] = {}


class StreamManager:
    """流管理器"""
    
    def __init__(self):
        self.active_streams: Dict[str, Dict] = {}
    
    async def start_stream(self, websocket: WebSocket, command: StreamCommand, algorithm_engine):
        """启动流处理"""
        stream_id = f"stream_{id(websocket)}"
        
        # 记录流信息
        self.active_streams[stream_id] = {
            "websocket": websocket,
            "config": command.stream_config,
            "parameters": command.parameters,
            "status": "running"
        }
        
        try:
            # 发送启动确认
            await websocket.send_json({
                "type": "status",
                "message": f"开始处理流: {command.stream_config.source_url}"
            })
            
            # 模拟流处理
            frame_count = 0
            while stream_id in self.active_streams:
                # 模拟处理延迟
                await asyncio.sleep(1.0 / command.stream_config.fps)
                
                frame_count += 1
                
                # 模拟检测结果（每5帧发送一次）
                if frame_count % 5 == 0:
                    # 生成模拟检测结果
                    detection_result = {
                        "type": "detection",
                        "timestamp": "2025-07-29T10:30:00.123Z",
                        "frame_id": frame_count,
                        "detections": [
                            {
                                "track_id": 1,
                                "class_name": "person",
                                "confidence": 0.85,
                                "bbox": {
                                    "x_min": 100,
                                    "y_min": 150,
                                    "x_max": 200,
                                    "y_max": 300,
                                    "width": 100,
                                    "height": 150
                                },
                                "center": {"x": 150, "y": 225}
                            }
                        ],
                        "stream_info": {
                            "fps": command.stream_config.fps,
                            "frame_count": frame_count,
                            "dropped_frames": 0
                        }
                    }
                    
                    await websocket.send_json(detection_result)
                
                # 定期发送状态更新
                if frame_count % 100 == 0:
                    await websocket.send_json({
                        "type": "status",
                        "message": f"已处理 {frame_count} 帧"
                    })
        
        except Exception as e:
            await websocket.send_json({
                "type": "error",
                "message": f"流处理错误: {str(e)}"
            })
        finally:
            # 清理流信息
            if stream_id in self.active_streams:
                del self.active_streams[stream_id]
    
    async def stop_stream(self, websocket: WebSocket):
        """停止流处理"""
        stream_id = f"stream_{id(websocket)}"
        if stream_id in self.active_streams:
            del self.active_streams[stream_id]
            await websocket.send_json({
                "type": "status",
                "message": "流处理已停止"
            })


# 全局流管理器
stream_manager = StreamManager()


@router.websocket("/stream")
async def websocket_stream_processing(
    websocket: WebSocket,
    algorithm_engine = Depends(get_algorithm_engine)
):
    """
    WebSocket流处理端点
    """
    await websocket.accept()
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                command = StreamCommand(**message)
                
                if command.action == "start":
                    # 启动流处理
                    await stream_manager.start_stream(websocket, command, algorithm_engine)
                
                elif command.action == "stop":
                    # 停止流处理
                    await stream_manager.stop_stream(websocket)
                    break
                
                else:
                    await websocket.send_json({
                        "type": "error",
                        "message": f"不支持的操作: {command.action}"
                    })
            
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error",
                    "message": "无效的JSON格式"
                })
            
            except Exception as e:
                await websocket.send_json({
                    "type": "error",
                    "message": f"处理命令失败: {str(e)}"
                })
    
    except WebSocketDisconnect:
        # 客户端断开连接
        await stream_manager.stop_stream(websocket)
    
    except Exception as e:
        print(f"WebSocket错误: {e}")
        await stream_manager.stop_stream(websocket)
