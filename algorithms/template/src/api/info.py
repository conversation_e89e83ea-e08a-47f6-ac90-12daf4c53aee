"""
算法信息API
"""

from typing import List, Dict, Any
from fastapi import APIRouter
from pydantic import BaseModel

from ..config import settings
from ..main import get_algorithm_engine, get_task_manager

router = APIRouter()


class Capabilities(BaseModel):
    """算法能力描述"""
    input_modes: List[str]
    supported_formats: List[str]
    max_file_size_mb: int
    concurrent_requests: int


class ModelInfo(BaseModel):
    """模型信息"""
    framework: str
    model_file: str
    input_size: List[int]
    classes: List[str]


class Performance(BaseModel):
    """性能指标"""
    avg_inference_time_ms: float
    throughput_fps: float
    memory_usage_mb: int


class CurrentLoad(BaseModel):
    """当前负载"""
    active_tasks: int
    queue_length: int
    cpu_percent: float
    memory_used_mb: int
    gpu_memory_used_mb: int = 0
    gpu_memory_total_mb: int = 0


class Statistics(BaseModel):
    """统计信息"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time_ms: float


class AlgorithmStatus(BaseModel):
    """算法状态响应"""
    status: str
    message: str
    uptime_seconds: int
    current_load: CurrentLoad
    statistics: Statistics


class AlgorithmInfo(BaseModel):
    """算法信息响应"""
    algorithm_name: str
    algorithm_version: str
    algorithm_type: str
    description: str
    capabilities: Capabilities
    model_info: ModelInfo
    performance: Performance


@router.get("/info", response_model=AlgorithmInfo)
async def get_algorithm_info():
    """
    获取算法元信息
    
    返回算法的详细信息，供平台展示和管理
    """
    return AlgorithmInfo(
        algorithm_name=settings.algorithm_name,
        algorithm_version=settings.algorithm_version,
        algorithm_type=settings.algorithm_type,
        description=settings.algorithm_description,
        capabilities=Capabilities(
            input_modes=["file", "stream", "batch"],
            supported_formats=["jpg", "jpeg", "png", "mp4", "avi"],
            max_file_size_mb=settings.max_file_size_mb,
            concurrent_requests=settings.max_concurrent_tasks
        ),
        model_info=ModelInfo(
            framework="PyTorch",
            model_file=settings.model_path,
            input_size=[640, 640],
            classes=["person", "car", "bicycle"]  # 根据实际模型修改
        ),
        performance=Performance(
            avg_inference_time_ms=50.0,
            throughput_fps=20.0,
            memory_usage_mb=512
        )
    )


@router.get("/status", response_model=AlgorithmStatus)
async def get_algorithm_status(
    algorithm_engine = Depends(get_algorithm_engine),
    task_manager = Depends(get_task_manager)
):
    """
    获取算法当前运行状态

    返回算法的实时状态信息，供平台监控
    """
    import psutil
    import time

    # 获取系统资源信息
    cpu_percent = psutil.cpu_percent()
    memory = psutil.virtual_memory()

    # 获取任务管理器状态
    active_tasks = task_manager.running_tasks
    queue_length = len([t for t in task_manager.tasks.values() if t.status.value == "pending"])

    return AlgorithmStatus(
        status="ready" if algorithm_engine.is_initialized else "loading",
        message="模型已加载，准备接受任务" if algorithm_engine.is_initialized else "模型加载中...",
        uptime_seconds=int(time.time()),  # 简化实现
        current_load=CurrentLoad(
            active_tasks=active_tasks,
            queue_length=queue_length,
            cpu_percent=cpu_percent,
            memory_used_mb=int(memory.used / 1024 / 1024),
            gpu_memory_used_mb=0,  # 需要根据实际GPU使用情况实现
            gpu_memory_total_mb=0
        ),
        statistics=Statistics(
            total_requests=len(task_manager.tasks),
            successful_requests=len([t for t in task_manager.tasks.values() if t.status.value == "completed"]),
            failed_requests=len([t for t in task_manager.tasks.values() if t.status.value == "failed"]),
            avg_response_time_ms=50.0  # 需要根据实际统计实现
        )
    )
