"""
异步处理API
"""

import asyncio
from typing import Optional
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, Depends, BackgroundTasks

from ..main import get_algorithm_engine, get_task_manager
from ..models.detection import (
    DetectionRequest, AsyncTaskResponse, TaskStatus
)

router = APIRouter()


@router.post("/process/async", response_model=AsyncTaskResponse)
async def process_file_async(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    parameters: Optional[str] = Form(None),
    algorithm_engine = Depends(get_algorithm_engine),
    task_manager = Depends(get_task_manager)
):
    """
    异步文件处理
    
    适用于大文件或长时间处理任务
    """
    # 验证文件类型
    if not file.content_type.startswith(('image/', 'video/')):
        raise HTTPException(
            status_code=400,
            detail="不支持的文件类型，仅支持图片和视频文件"
        )
    
    # 创建任务
    job_id = task_manager.create_task()
    
    # 解析参数
    request = DetectionRequest.from_form_data(parameters)
    
    # 读取文件内容
    file_content = await file.read()
    
    # 添加后台任务
    background_tasks.add_task(
        task_manager.execute_task,
        job_id,
        algorithm_engine.detect,
        file_content,
        file.filename,
        request
    )
    
    return AsyncTaskResponse(
        job_id=job_id,
        status="pending",
        message="任务已接收，正在排队处理",
        estimated_time_seconds=60
    )


@router.get("/results/{job_id}", response_model=TaskStatus)
async def get_task_result(
    job_id: str,
    task_manager = Depends(get_task_manager)
):
    """
    查询异步任务结果
    """
    task_status = task_manager.get_task_status(job_id)
    
    if not task_status:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    return task_status


@router.delete("/results/{job_id}")
async def cancel_task(
    job_id: str,
    task_manager = Depends(get_task_manager)
):
    """
    取消异步任务
    """
    task_status = task_manager.get_task_status(job_id)
    
    if not task_status:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    if task_status.status in ["completed", "failed"]:
        raise HTTPException(
            status_code=400,
            detail="任务已完成，无法取消"
        )
    
    # 这里可以实现任务取消逻辑
    # 目前只是简单返回成功
    
    return {"message": "任务取消请求已接收"}
