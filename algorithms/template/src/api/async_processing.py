"""
异步处理API - v2.0统一响应格式
"""

import asyncio
import time
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, Depends, BackgroundTasks

from ..main import get_algorithm_engine, get_task_manager
from ..models.detection import (
    DetectionRequest,
    UnifiedResponse,
    AsyncTaskData,
    Metadata,
    create_success_response,
    create_error_response
)

router = APIRouter()


@router.post("/process/async", response_model=UnifiedResponse)
async def process_file_async(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    parameters: Optional[str] = Form(None),
    algorithm_engine = Depends(get_algorithm_engine),
    task_manager = Depends(get_task_manager)
):
    """
    异步文件处理 - v2.0统一响应格式

    适用于大文件或长时间处理任务
    """
    start_time = time.time()

    try:
        # 验证文件类型
        if not file.content_type.startswith(('image/', 'video/')):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件类型",
                error_details="仅支持图片和视频文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 创建任务
        job_id = task_manager.create_task()

        # 解析参数
        request = DetectionRequest.from_form_data(parameters)

        # 读取文件内容
        file_content = await file.read()

        # 添加后台任务
        background_tasks.add_task(
            task_manager.execute_task,
            job_id,
            algorithm_engine.detect,
            file_content,
            file.filename,
            request
        )

        # 创建异步任务响应数据
        task_data = AsyncTaskData(
            job_id=job_id,
            status="pending",
            message="任务已接收，正在排队处理",
            estimated_time_seconds=60
        )

        metadata = Metadata(
            processing_time_ms=(time.time() - start_time) * 1000,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z"
        )

        return create_success_response(
            data=task_data,
            metadata=metadata
        )

    except Exception as e:
        return create_error_response(
            error_code="TASK_CREATION_ERROR",
            error_message="任务创建失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@router.get("/results/{job_id}", response_model=UnifiedResponse)
async def get_task_result(
    job_id: str,
    task_manager = Depends(get_task_manager)
):
    """
    查询异步任务结果 - v2.0统一响应格式
    """
    start_time = time.time()

    try:
        task_status = task_manager.get_task_status(job_id)

        if not task_status:
            return create_error_response(
                error_code="TASK_NOT_FOUND",
                error_message="任务不存在",
                error_details=f"任务ID {job_id} 不存在",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        metadata = Metadata(
            processing_time_ms=(time.time() - start_time) * 1000,
            image_shape=getattr(task_status, 'image_shape', [0, 0, 0]),
            timestamp_utc=datetime.utcnow().isoformat() + "Z"
        )

        return create_success_response(
            data=task_status,
            metadata=metadata
        )

    except Exception as e:
        return create_error_response(
            error_code="QUERY_ERROR",
            error_message="查询任务状态失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@router.delete("/results/{job_id}", response_model=UnifiedResponse)
async def cancel_task(
    job_id: str,
    task_manager = Depends(get_task_manager)
):
    """
    取消异步任务 - v2.0统一响应格式
    """
    start_time = time.time()

    try:
        task_status = task_manager.get_task_status(job_id)

        if not task_status:
            return create_error_response(
                error_code="TASK_NOT_FOUND",
                error_message="任务不存在",
                error_details=f"任务ID {job_id} 不存在",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        if hasattr(task_status, 'status') and task_status.status in ["completed", "failed"]:
            return create_error_response(
                error_code="TASK_CANNOT_CANCEL",
                error_message="任务已完成，无法取消",
                error_details=f"任务状态: {task_status.status}",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 这里可以实现任务取消逻辑
        # 目前只是简单返回成功

        cancel_data = {
            "job_id": job_id,
            "message": "任务取消请求已接收",
            "status": "cancelled"
        }

        metadata = Metadata(
            processing_time_ms=(time.time() - start_time) * 1000,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z"
        )

        return create_success_response(
            data=cancel_data,
            metadata=metadata
        )

    except Exception as e:
        return create_error_response(
            error_code="CANCEL_ERROR",
            error_message="取消任务失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )
