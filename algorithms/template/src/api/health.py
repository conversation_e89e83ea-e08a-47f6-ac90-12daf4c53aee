"""
健康检查API
"""

from datetime import datetime
from fastapi import APIRouter
from pydantic import BaseModel

router = APIRouter()


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: datetime


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    健康检查端点
    
    用于Docker健康检查和负载均衡器探针
    """
    return HealthResponse(
        status="ok",
        timestamp=datetime.utcnow()
    )
