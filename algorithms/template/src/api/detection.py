"""
检测API - 同步处理
"""

import time
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, Depends
from pydantic import BaseModel

from ..main import get_algorithm_engine
from ..models.detection import DetectionRequest, DetectionResponse

router = APIRouter()


@router.post("/detect", response_model=DetectionResponse)
async def detect_objects(
    file: UploadFile = File(...),
    parameters: Optional[str] = Form(None),
    algorithm_engine = Depends(get_algorithm_engine)
):
    """
    同步目标检测
    
    适用于快速检测场景，直接返回检测结果
    """
    # 验证文件类型
    if not file.content_type.startswith(('image/', 'video/')):
        raise HTTPException(
            status_code=400,
            detail="不支持的文件类型，仅支持图片和视频文件"
        )
    
    # 解析参数
    request = DetectionRequest.from_form_data(parameters)
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 执行检测
        results = await algorithm_engine.detect(
            file_content=file_content,
            filename=file.filename,
            parameters=request
        )
        
        # 计算处理时间
        processing_time = int((time.time() - start_time) * 1000)
        
        return DetectionResponse(
            success=True,
            processing_time_ms=processing_time,
            results=results
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"检测处理失败: {str(e)}"
        )


@router.post("/predict", response_model=DetectionResponse)
async def predict(
    file: UploadFile = File(...),
    parameters: Optional[str] = Form(None),
    algorithm_engine = Depends(get_algorithm_engine)
):
    """
    通用预测接口
    
    兼容不同类型的算法（检测、分类、分割等）
    """
    return await detect_objects(file, parameters, algorithm_engine)
