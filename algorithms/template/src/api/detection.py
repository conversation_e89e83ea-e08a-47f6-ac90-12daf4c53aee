"""
检测API - 同步处理 (v2.0统一响应格式)
"""

import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, Depends
from pydantic import BaseModel

from ..main import get_algorithm_engine
from ..models.detection import (
    DetectionRequest,
    UnifiedResponse,
    DetectionData,
    Metadata,
    ModelInfo,
    HardwareInfo,
    create_success_response,
    create_error_response
)

router = APIRouter()


@router.post("/detect", response_model=UnifiedResponse)
async def detect_objects(
    file: UploadFile = File(...),
    parameters: Optional[str] = Form(None),
    algorithm_engine = Depends(get_algorithm_engine)
):
    """
    同步目标检测 - v2.0统一响应格式

    适用于快速检测场景，直接返回检测结果
    """
    start_time = time.time()

    try:
        # 验证文件类型
        if not file.content_type.startswith(('image/', 'video/')):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件类型",
                error_details="仅支持图片和视频文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 解析参数
        request = DetectionRequest.from_form_data(parameters)

        # 读取文件内容
        file_content = await file.read()

        # 执行检测
        detection_data = await algorithm_engine.detect(
            file_content=file_content,
            filename=file.filename,
            parameters=request
        )

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=detection_data.get('image_shape', [0, 0, 0]),
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="template_algorithm",
                version="1.0.0"
            ),
            hardware_info=HardwareInfo(
                device="cpu"
            )
        )

        return create_success_response(
            data=detection_data,
            metadata=metadata
        )

    except Exception as e:
        return create_error_response(
            error_code="PROCESSING_ERROR",
            error_message="检测处理失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@router.post("/predict", response_model=UnifiedResponse)
async def predict(
    file: UploadFile = File(...),
    parameters: Optional[str] = Form(None),
    algorithm_engine = Depends(get_algorithm_engine)
):
    """
    通用预测接口 - v2.0统一响应格式

    兼容不同类型的算法（检测、分类、分割等）
    """
    return await detect_objects(file, parameters, algorithm_engine)
