"""
配置管理
"""

from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 算法基本信息
    algorithm_name: str = "算法模板"
    algorithm_version: str = "1.0.0"
    algorithm_type: str = "目标检测"
    algorithm_description: str = "算法容器开发模板，遵循统一API规范"
    
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # CORS配置
    cors_origins: List[str] = ["*"]
    
    # 模型配置
    model_path: str = "models/model.pt"
    model_device: str = "cpu"  # cpu, cuda, mps
    model_confidence_threshold: float = 0.5
    model_iou_threshold: float = 0.4
    
    # 处理配置
    max_file_size_mb: int = 100
    max_concurrent_tasks: int = 5
    task_timeout_seconds: int = 300
    
    # 存储配置
    static_dir: str = "static"
    results_dir: str = "static/results"
    
    # Redis配置（用于异步任务）
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/algorithm.log"
    
    # 监控配置
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    class Config:
        env_file = ".env"
        env_prefix = "ALGORITHM_"


# 全局配置实例
settings = Settings()
