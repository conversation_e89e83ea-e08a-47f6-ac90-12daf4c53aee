"""
检测相关数据模型
"""

import json
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class BoundingBox(BaseModel):
    """边界框"""
    x_min: float = Field(..., description="左上角X坐标")
    y_min: float = Field(..., description="左上角Y坐标")
    x_max: float = Field(..., description="右下角X坐标")
    y_max: float = Field(..., description="右下角Y坐标")
    width: float = Field(..., description="宽度")
    height: float = Field(..., description="高度")


class Point(BaseModel):
    """点坐标"""
    x: float
    y: float


class Detection(BaseModel):
    """单个检测结果"""
    class_id: int = Field(..., description="类别ID")
    class_name: str = Field(..., description="类别名称")
    confidence: float = Field(..., ge=0.0, le=1.0, description="置信度")
    bbox: BoundingBox = Field(..., description="边界框")
    center: Point = Field(..., description="中心点")
    track_id: Optional[int] = Field(None, description="跟踪ID（仅流处理）")


class ImageSize(BaseModel):
    """图像尺寸"""
    width: int
    height: int


class DetectionSummary(BaseModel):
    """检测结果摘要"""
    total_detections: int = Field(..., description="检测总数")
    classes_detected: List[str] = Field(..., description="检测到的类别")
    image_size: ImageSize = Field(..., description="图像尺寸")


class DetectionResults(BaseModel):
    """检测结果"""
    detections: List[Detection] = Field(..., description="检测结果列表")
    summary: DetectionSummary = Field(..., description="结果摘要")


class OutputFiles(BaseModel):
    """输出文件"""
    annotated_image: Optional[str] = Field(None, description="标注图片URL")
    annotated_video: Optional[str] = Field(None, description="标注视频URL")
    detection_log: Optional[str] = Field(None, description="检测日志URL")


class DetectionRequest(BaseModel):
    """检测请求参数"""
    confidence_threshold: float = Field(0.5, ge=0.0, le=1.0, description="置信度阈值")
    iou_threshold: float = Field(0.4, ge=0.0, le=1.0, description="IoU阈值")
    max_detections: int = Field(100, ge=1, le=1000, description="最大检测数量")
    return_crops: bool = Field(False, description="是否返回裁剪图片")
    annotate_image: bool = Field(True, description="是否生成标注图片")
    
    @classmethod
    def from_form_data(cls, parameters: Optional[str] = None) -> "DetectionRequest":
        """从表单数据创建请求对象"""
        if parameters:
            try:
                params = json.loads(parameters)
                return cls(**params)
            except (json.JSONDecodeError, TypeError):
                pass
        return cls()


class DetectionResponse(BaseModel):
    """检测响应"""
    success: bool = Field(..., description="是否成功")
    processing_time_ms: int = Field(..., description="处理时间（毫秒）")
    results: DetectionResults = Field(..., description="检测结果")
    output_files: Optional[OutputFiles] = Field(None, description="输出文件")
    error: Optional[str] = Field(None, description="错误信息")


class AsyncTaskResponse(BaseModel):
    """异步任务响应"""
    job_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="状态描述")
    estimated_time_seconds: Optional[int] = Field(None, description="预估处理时间")


class TaskStatus(BaseModel):
    """任务状态"""
    job_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    progress_percent: Optional[int] = Field(None, ge=0, le=100, description="进度百分比")
    message: Optional[str] = Field(None, description="状态消息")
    estimated_remaining_seconds: Optional[int] = Field(None, description="预估剩余时间")
    processing_time_seconds: Optional[float] = Field(None, description="已处理时间")
    results: Optional[DetectionResults] = Field(None, description="处理结果")
    output_files: Optional[OutputFiles] = Field(None, description="输出文件")
    error_message: Optional[str] = Field(None, description="错误信息")
