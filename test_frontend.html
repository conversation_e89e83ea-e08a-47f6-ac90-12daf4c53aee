<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法平台测试 - v2.0统一响应格式</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-section {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            transition: border-color 0.3s;
        }
        
        .upload-section:hover {
            border-color: #007bff;
        }
        
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        input[type="file"] {
            margin: 10px 0;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .image-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .image-box {
            flex: 1;
            min-width: 300px;
        }
        
        .image-box h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .image-display {
            position: relative;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .image-display img {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .detection-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .detection-box {
            position: absolute;
            border: 2px solid #ff0000;
            background: rgba(255, 0, 0, 0.1);
            pointer-events: none;
        }
        
        .detection-label {
            position: absolute;
            background: #ff0000;
            color: white;
            padding: 2px 6px;
            font-size: 12px;
            top: -20px;
            left: 0;
            white-space: nowrap;
        }
        
        .response-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .detection-stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .detection-stats h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 3px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 算法平台测试 - v2.0统一响应格式</h1>
        
        <div class="upload-section" id="uploadSection">
            <h3>📁 上传图片进行检测</h3>
            <p>支持拖拽上传或点击选择文件</p>
            <input type="file" id="fileInput" accept="image/*" />
            <br>
            <button onclick="detectImage()" id="detectBtn">🔍 开始检测</button>
            <button onclick="testHealthCheck()" id="healthBtn">💚 健康检查</button>
        </div>
        
        <div id="statusDiv"></div>
        
        <div class="results-section" id="resultsSection" style="display: none;">
            <h2>📊 检测结果</h2>
            
            <div class="detection-stats" id="statsDiv"></div>
            
            <div class="image-container">
                <div class="image-box">
                    <h3>原始图片</h3>
                    <div class="image-display">
                        <img id="originalImage" />
                    </div>
                </div>
                
                <div class="image-box">
                    <h3>检测结果</h3>
                    <div class="image-display">
                        <img id="resultImage" />
                        <div class="detection-overlay" id="detectionOverlay"></div>
                    </div>
                </div>
            </div>
            
            <h3>📋 API响应数据 (v2.0格式)</h3>
            <div class="response-data" id="responseData"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8002/api/v1';
        
        // 拖拽上传功能
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');
        
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
            }
        });
        
        function showStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('statusDiv');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function hideResults() {
            document.getElementById('resultsSection').style.display = 'none';
        }
        
        function showResults() {
            document.getElementById('resultsSection').style.display = 'block';
        }
        
        async function testHealthCheck() {
            showStatus('🔍 正在检查服务健康状态...', 'loading');
            hideResults();
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                if (data.success) {
                    showStatus(`✅ 服务运行正常 - ${data.data.service} (${data.metadata.model_info.version})`, 'success');
                    document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                    showResults();
                } else {
                    showStatus(`❌ 服务异常: ${data.error.message}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 连接失败: ${error.message}`, 'error');
            }
        }
        
        async function detectImage() {
            const file = fileInput.files[0];
            if (!file) {
                showStatus('⚠️ 请先选择一张图片', 'error');
                return;
            }
            
            showStatus('🔄 正在上传图片并进行检测...', 'loading');
            hideResults();
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch(`${API_BASE_URL}/detect`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus(`✅ 检测完成！发现 ${data.data.summary.num_detections} 个目标`, 'success');
                    displayResults(data, file);
                } else {
                    showStatus(`❌ 检测失败: ${data.error.message}`, 'error');
                    document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
                    showResults();
                }
            } catch (error) {
                showStatus(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        function displayResults(data, originalFile) {
            // 显示统计信息
            displayStats(data);
            
            // 显示原始图片
            const originalImage = document.getElementById('originalImage');
            originalImage.src = URL.createObjectURL(originalFile);
            
            // 显示检测结果图片（复制原图）
            const resultImage = document.getElementById('resultImage');
            resultImage.src = URL.createObjectURL(originalFile);
            
            // 等待图片加载完成后绘制检测框
            resultImage.onload = () => {
                drawDetectionBoxes(data.data.detections, resultImage);
            };
            
            // 显示响应数据
            document.getElementById('responseData').textContent = JSON.stringify(data, null, 2);
            
            showResults();
        }
        
        function displayStats(data) {
            const statsDiv = document.getElementById('statsDiv');
            const summary = data.data.summary;
            const metadata = data.metadata;
            
            let classCountsHtml = '';
            for (const [className, count] of Object.entries(summary.class_counts)) {
                classCountsHtml += `<div class="stat-item">
                    <div class="stat-value">${count}</div>
                    <div class="stat-label">${className}</div>
                </div>`;
            }
            
            const confidenceStats = summary.confidence_stats || {};
            
            statsDiv.innerHTML = `
                <h4>📈 检测统计</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${summary.num_detections}</div>
                        <div class="stat-label">总检测数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${metadata.processing_time_ms.toFixed(1)}ms</div>
                        <div class="stat-label">处理时间</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${confidenceStats.avg ? (confidenceStats.avg * 100).toFixed(1) + '%' : 'N/A'}</div>
                        <div class="stat-label">平均置信度</div>
                    </div>
                    ${classCountsHtml}
                </div>
            `;
        }
        
        function drawDetectionBoxes(detections, imageElement) {
            const overlay = document.getElementById('detectionOverlay');
            overlay.innerHTML = '';
            
            const imageRect = imageElement.getBoundingClientRect();
            const imageWidth = imageElement.naturalWidth;
            const imageHeight = imageElement.naturalHeight;
            const displayWidth = imageElement.offsetWidth;
            const displayHeight = imageElement.offsetHeight;
            
            const scaleX = displayWidth / imageWidth;
            const scaleY = displayHeight / imageHeight;
            
            detections.forEach((detection, index) => {
                const [x1, y1, x2, y2] = detection.bbox;
                
                // 创建检测框
                const box = document.createElement('div');
                box.className = 'detection-box';
                box.style.left = (x1 * scaleX) + 'px';
                box.style.top = (y1 * scaleY) + 'px';
                box.style.width = ((x2 - x1) * scaleX) + 'px';
                box.style.height = ((y2 - y1) * scaleY) + 'px';
                
                // 创建标签
                const label = document.createElement('div');
                label.className = 'detection-label';
                label.textContent = `${detection.label} (${(detection.confidence * 100).toFixed(1)}%)`;
                
                box.appendChild(label);
                overlay.appendChild(box);
            });
        }
        
        // 页面加载时自动进行健康检查
        window.onload = () => {
            testHealthCheck();
        };
    </script>
</body>
</html>
